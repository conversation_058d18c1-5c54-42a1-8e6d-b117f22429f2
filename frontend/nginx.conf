server {
    listen 80;
    server_name localhost;
    
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
        
        # Enable gzip compression
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        # Cache control
        expires 1y;
        add_header Cache-Control "public, no-transform";
    }
    
    # Disable caching for service worker
    location /service-worker.js {
        add_header Cache-Control "no-store, no-cache, must-revalidate";
    }
}