<template>
  <div class="fixed inset-0 flex items-center justify-center z-50 password-change-prompt">
    <div class="fixed inset-0 bg-black bg-opacity-50" @click.stop></div>
    <div class="relative z-10 bg-white rounded-lg shadow-lg w-full max-w-md password-change-prompt">
      <div class="flex justify-between items-center p-4 border-b">
        <h2 class="text-lg font-medium">Password Reset Required</h2>
      </div>

      <div class="p-6">
        <p class="mb-4 text-gray-700">
          For security reasons, you need to change your temporary password. After changing your password, you will be logged out and need to log in again with your new password.
        </p>

        <div class="mb-4 p-3 bg-blue-50 text-blue-700 rounded-md text-sm">
          <p class="font-medium">Your password must:</p>
          <ul class="list-disc ml-5 mt-1">
            <li>Be at least 8 characters long</li>
            <li>Include at least one uppercase letter (A-Z)</li>
            <li>Include at least one lowercase letter (a-z)</li>
            <li>Include at least one number (0-9)</li>
            <li>Include at least one special character (!@#$%^&*)</li>
          </ul>
        </div>

        <form @submit.prevent="changePassword" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
            <div class="relative">
              <input
                :type="showNewPassword ? 'text' : 'password'"
                v-model="newPassword"
                class="w-full p-2 border border-gray-300 rounded-md"
                required
                @input="validatePassword"
              />
              <button
                type="button"
                @click="showNewPassword = !showNewPassword"
                class="absolute right-2 top-2 text-gray-500"
              >
                <i :class="showNewPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div v-if="passwordStrength" class="mt-1">
              <div class="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                <div
                  class="h-full rounded-full"
                  :class="passwordStrengthClass"
                  :style="{ width: `${passwordStrength}%` }"
                ></div>
              </div>
              <p class="text-xs mt-1" :class="passwordStrengthTextClass">
                {{ passwordStrengthText }}
              </p>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
            <div class="relative">
              <input
                :type="showConfirmPassword ? 'text' : 'password'"
                v-model="confirmPassword"
                class="w-full p-2 border border-gray-300 rounded-md"
                required
                @input="validateConfirmPassword"
              />
              <button
                type="button"
                @click="showConfirmPassword = !showConfirmPassword"
                class="absolute right-2 top-2 text-gray-500"
              >
                <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <p v-if="confirmPasswordError" class="text-xs text-red-500 mt-1">
              {{ confirmPasswordError }}
            </p>
            <p v-else-if="confirmPassword && !confirmPasswordError" class="text-xs text-green-500 mt-1">
              Passwords match
            </p>
          </div>

          <div v-if="error" class="p-3 bg-red-100 text-red-700 rounded-md text-sm">
            {{ error }}
          </div>

          <div class="flex justify-end">
            <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200"
              :disabled="loading || !isPasswordValid || confirmPasswordError"
              :class="{ 'opacity-50 cursor-not-allowed': loading || !isPasswordValid || confirmPasswordError }"
            >
              <span v-if="loading">
                <i class="fas fa-spinner fa-spin mr-2"></i>
                Updating...
              </span>
              <span v-else>Reset Password & Log Out</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import api from '@/services/apiService';

export default {
  name: 'PasswordChangePrompt',
  emits: ['password-changed'],
  setup(_, { emit }) {
    const newPassword = ref('');
    const confirmPassword = ref('');
    const showNewPassword = ref(false);
    const showConfirmPassword = ref(false);
    const error = ref('');
    const loading = ref(false);
    const passwordStrength = ref(0);
    const confirmPasswordError = ref('');
    const isPasswordValid = ref(false);

    // Password strength indicators
    const passwordStrengthText = computed(() => {
      if (passwordStrength.value === 0) return '';
      if (passwordStrength.value < 30) return 'Weak';
      if (passwordStrength.value < 60) return 'Medium';
      if (passwordStrength.value < 80) return 'Strong';
      return 'Very Strong';
    });

    const passwordStrengthClass = computed(() => {
      if (passwordStrength.value < 30) return 'bg-red-500';
      if (passwordStrength.value < 60) return 'bg-yellow-500';
      if (passwordStrength.value < 80) return 'bg-blue-500';
      return 'bg-green-500';
    });

    const passwordStrengthTextClass = computed(() => {
      if (passwordStrength.value < 30) return 'text-red-500';
      if (passwordStrength.value < 60) return 'text-yellow-500';
      if (passwordStrength.value < 80) return 'text-blue-500';
      return 'text-green-500';
    });

    // Validate password as user types
    const validatePassword = () => {
      const password = newPassword.value;

      // Reset strength
      let strength = 0;
      isPasswordValid.value = false;

      if (password.length === 0) {
        passwordStrength.value = 0;
        return;
      }

      // Length check
      if (password.length >= 8) {
        strength += 20;
      } else {
        passwordStrength.value = Math.max(10, password.length * 2);
        return;
      }

      // Character type checks
      const hasUppercase = /[A-Z]/.test(password);
      const hasLowercase = /[a-z]/.test(password);
      const hasNumber = /[0-9]/.test(password);
      const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

      if (hasUppercase) strength += 20;
      if (hasLowercase) strength += 20;
      if (hasNumber) strength += 20;
      if (hasSpecial) strength += 20;

      // Set password strength
      passwordStrength.value = strength;

      // Check if password meets all requirements
      isPasswordValid.value = (
        password.length >= 8 &&
        hasUppercase &&
        hasLowercase &&
        hasNumber &&
        hasSpecial
      );

      // Validate confirm password if it exists
      if (confirmPassword.value) {
        validateConfirmPassword();
      }
    };

    // Validate confirm password
    const validateConfirmPassword = () => {
      if (!confirmPassword.value) {
        confirmPasswordError.value = '';
        return;
      }

      if (newPassword.value !== confirmPassword.value) {
        confirmPasswordError.value = 'Passwords do not match';
      } else {
        confirmPasswordError.value = '';
      }
    };

    const changePassword = async () => {
      // Reset error
      error.value = '';

      // Validate passwords match
      if (newPassword.value !== confirmPassword.value) {
        error.value = 'Passwords do not match';
        return;
      }

      // Validate password strength
      if (!isPasswordValid.value) {
        error.value = 'Password does not meet the requirements';
        return;
      }

      try {
        loading.value = true;

        // Get the current user from localStorage
        const user = JSON.parse(localStorage.getItem('user'));
        const subsidiary = localStorage.getItem('selectedSubsidiary');

        // Call API to change password
        await api.post('/auth/change-password', {
          userId: user.id,
          newPassword: newPassword.value
        }, {
          headers: {
            'x-subsidiary': subsidiary
          }
        });

        // We don't need to update the user in localStorage
        // as we'll be logging out and the user will log in again

        // Emit event to parent component to handle logout
        emit('password-changed');
      } catch (err) {
        console.error('Error changing password:', err);
        error.value = err.response?.data?.message || 'Failed to change password';
      } finally {
        loading.value = false;
      }
    };

    return {
      newPassword,
      confirmPassword,
      showNewPassword,
      showConfirmPassword,
      error,
      loading,
      passwordStrength,
      passwordStrengthText,
      passwordStrengthClass,
      passwordStrengthTextClass,
      confirmPasswordError,
      isPasswordValid,
      validatePassword,
      validateConfirmPassword,
      changePassword
    };
  }
};
</script>
