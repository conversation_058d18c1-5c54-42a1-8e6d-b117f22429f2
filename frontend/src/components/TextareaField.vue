<template>
  <div class="flex flex-col">
    <label :for="id" class="font-medium mb-1">{{ label }}</label>
    <textarea
      :id="id"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      class="border rounded p-2 w-full"
      required
    ></textarea>
  </div>
</template>

<script>
export default {
  props: {
    id: String,
    label: String,
    modelValue: String,
  },
  emits: ["update:modelValue"],
};
</script>
