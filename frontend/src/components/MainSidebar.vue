<template>
  <div
    class="sidebar"
    :style="{
      background: `linear-gradient(to bottom, ${theme.primaryColor}, ${theme.secondaryColor})`,
    }"
  >
    <!-- Logo -->
    <div class="logo-container">
      <img :src="theme.logo" alt="Company Logo" class="company-logo" />
    </div>

    <h2 class="sidebar-title">MENU</h2>

    <ul class="menu-list">
      <!-- Dashboard -->
      <li>
        <router-link :to="`/dashboard/${subsidiary}`" class="menu-item">
          <i class="fas fa-home"></i>
          <span>Dashboard</span>
        </router-link>
      </li>



      <!-- User Access Granting -->
      <li>
        <div @click="toggleGranting" class="menu-dropdown">
          <div class="menu-header">
            <i class="fas fa-user-shield"></i>
            <span>User Access Granting</span>
          </div>
          <i
            :class="grantingOpen ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"
          ></i>
        </div>
        <ul v-show="grantingOpen" class="submenu">
          <li v-if="hasRequestFormAccess">
            <router-link :to="`/request/${subsidiary}`" class="submenu-item">
              <i class="fas fa-file-alt"></i>
              <span>Access Form Request</span>
            </router-link>
          </li>
          <li v-if="hasApprovalAccess">
            <router-link :to="`/approval/${subsidiary}`" class="submenu-item">
              <i class="fas fa-check"></i>
              <span>Approval Flow</span>
            </router-link>
          </li>
          <li v-if="hasAuditAccess">
            <router-link :to="`/audit/${subsidiary}`" class="submenu-item">
              <i class="fas fa-book"></i>
              <span>User Audit Trail</span>
            </router-link>
          </li>
          <li v-if="hasReportsAccess">
            <router-link :to="`/reports/${subsidiary}`" class="submenu-item">
              <i class="fas fa-chart-line"></i>
              <span>Access Request Reports</span>
            </router-link>
          </li>
          <li v-if="hasReportsAccess">
            <router-link :to="`/access-request-report/${subsidiary}`" class="submenu-item">
              <i class="fas fa-file-download"></i>
              <span>Access Request Details</span>
            </router-link>
          </li>
        </ul>
      </li>

      <!-- User Access Revocation -->
      <li>
        <div @click="toggleRevocation" class="menu-dropdown">
          <div class="menu-header">
            <i class="fas fa-user-slash"></i>
            <span>User Access Revocation</span>
          </div>
          <i
            :class="
              revocationOpen ? 'fas fa-chevron-up' : 'fas fa-chevron-down'
            "
          ></i>
        </div>
        <ul v-show="revocationOpen" class="submenu">
          <li v-if="hasRevokeFormAccess">
            <router-link
              :to="`/revoke-form/${subsidiary}`"
              class="submenu-item"
            >
              <i class="fas fa-user-minus"></i>
              <span>Form of a Revoke User</span>
            </router-link>
          </li>
          <!-- <li>
            <router-link
              :to="`/revoke-approval/${subsidiary}`"
              class="submenu-item"
            >
              <i class="fas fa-check-double"></i>
              <span>Revoke Approval</span>
            </router-link>
          </li> -->
          <li v-if="hasAccessRemovalAccess">
            <router-link
              :to="`/access-removal/${subsidiary}`"
              class="submenu-item"
            >
              <i class="fas fa-ban"></i>
              <span>Access Removal</span>
            </router-link>
          </li>
          <li v-if="hasRevocationAuditAccess">
            <router-link
              :to="`/revocation-audit/${subsidiary}`"
              class="submenu-item"
            >
              <i class="fas fa-book"></i>
              <span>Revocation Audit Trail</span>
            </router-link>
          </li>
          <li v-if="hasRevokedUsersReportAccess">
            <router-link
              :to="`/revoked-users/${subsidiary}`"
              class="submenu-item"
            >
              <i class="fas fa-chart-line"></i>
              <span>Revoked Users Reports</span>
            </router-link>
          </li>
        </ul>
      </li>
    </ul>
  </div>
</template>

<script>
import { subsidiaries } from "@/config/subsidiaries";
import { useStore } from 'vuex';
import { computed } from 'vue';
import { canAccess } from "@/utils/accessControl";

export default {
  props: ["subsidiary"],
  setup(props) {
    const store = useStore();

    // Get current user from store
    const currentUser = computed(() => store.getters['auth/currentUser']);

    console.log('MainSidebar setup - Current user:', currentUser.value);
    console.log('MainSidebar setup - Subsidiary:', props.subsidiary);

    // Check if user has access to specific resources
    const hasUserManagementAccess = computed(() => {
      const hasAccess = canAccess('users', currentUser.value);
      console.log('User management access:', hasAccess);
      return hasAccess;
    });

    const hasTenantManagementAccess = computed(() => {
      const hasAccess = canAccess('tenants', currentUser.value);
      console.log('Tenant management access:', hasAccess);
      return hasAccess;
    });

    const hasRequestFormAccess = computed(() => {
      const hasAccess = canAccess('request-form', currentUser.value);
      console.log('Request form access:', hasAccess);
      return hasAccess;
    });

    const hasApprovalAccess = computed(() => {
      const hasAccess = canAccess('approval', currentUser.value);
      console.log('Approval access:', hasAccess);
      return hasAccess;
    });

    const hasAuditAccess = computed(() => {
      const hasAccess = canAccess('audit', currentUser.value);
      console.log('Audit access:', hasAccess);
      return hasAccess;
    });

    const hasReportsAccess = computed(() => {
      const hasAccess = canAccess('reports', currentUser.value);
      console.log('Reports access:', hasAccess);
      return hasAccess;
    });

    const hasRevokeFormAccess = computed(() => {
      const hasAccess = canAccess('revoke-form', currentUser.value);
      console.log('Revoke form access:', hasAccess);
      return hasAccess;
    });

    const hasAccessRemovalAccess = computed(() => {
      const hasAccess = canAccess('access-removal', currentUser.value);
      console.log('Access removal access:', hasAccess);
      return hasAccess;
    });

    const hasRevocationAuditAccess = computed(() => {
      const hasAccess = canAccess('revocation-audit', currentUser.value);
      console.log('Revocation audit access:', hasAccess);
      return hasAccess;
    });

    const hasRevokedUsersReportAccess = computed(() => {
      const hasAccess = canAccess('revoked-users-report', currentUser.value);
      console.log('Revoked users report access:', hasAccess);
      return hasAccess;
    });

    return {
      hasUserManagementAccess,
      hasTenantManagementAccess,
      hasRequestFormAccess,
      hasApprovalAccess,
      hasAuditAccess,
      hasReportsAccess,
      hasRevokeFormAccess,
      hasAccessRemovalAccess,
      hasRevocationAuditAccess,
      hasRevokedUsersReportAccess,
      props
    };
  },
  computed: {
    theme() {
      console.log('MainSidebar computed theme - Subsidiary:', this.subsidiary);
      const theme = subsidiaries[this.subsidiary] || subsidiaries['platinumkenya'];
      console.log('MainSidebar computed theme - Theme:', theme);
      return theme;
    },
  },
  data() {
    return {
      grantingOpen: true,
      revocationOpen: true,
    };
  },
  methods: {
    toggleGranting() {
      this.grantingOpen = !this.grantingOpen;
    },
    toggleRevocation() {
      this.revocationOpen = !this.revocationOpen;
    },
  },
};
</script>

<style scoped>
@import "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css";

/* Sidebar */
.sidebar {
  width: 260px;
  min-height: 100vh;
  color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
  transition: background 0.3s ease-in-out;
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--secondary-color)
  );
}

/* Logo */
.logo-container {
  text-align: center;
  margin-bottom: 10px;
}

.company-logo {
  max-width: 180px;
  height: auto;
}

/* Sidebar Title */
.sidebar-title {
  font-size: 1.7rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Menu List */
.menu-list {
  list-style-type: none;
  padding: 0;
}

.menu-item:hover,
.submenu-item:hover {
  background: rgba(255, 255, 255, 0.3);
  font-weight: bold;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.3s ease-in-out, color 0.3s;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
}

.menu-item i {
  margin-right: 10px;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

/* Dropdown Menus */
.menu-dropdown {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  border-radius: 8px;
  transition: background 0.3s ease-in-out;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
}

.menu-dropdown:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Submenu */
.submenu {
  margin-left: 20px;
  padding-left: 10px;
  border-left: 2px solid rgba(255, 255, 255, 0.6);
}

.submenu-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 6px;
  transition: background 0.3s ease-in-out;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
}

.submenu-item i {
  margin-right: 8px;
}

.submenu-item:hover {
  background: rgba(255, 255, 255, 0.2);
}
</style>
