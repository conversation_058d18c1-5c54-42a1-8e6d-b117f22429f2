<template>
  <div class="p-4 bg-white rounded-lg shadow-md">
    <h3 class="text-lg font-semibold mb-4">Reset Password</h3>
    
    <form @submit.prevent="resetPassword">
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2" for="currentPassword">
          Current Password
        </label>
        <div class="relative">
          <input
            id="currentPassword"
            v-model="passwordForm.currentPassword"
            :type="showCurrentPassword ? 'text' : 'password'"
            class="w-full rounded border border-gray-300 px-4 py-2 text-sm"
            required
          />
          <button
            type="button"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400"
            @click="toggleCurrentPasswordVisibility"
          >
            <i class="fas" :class="showCurrentPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
          </button>
        </div>
      </div>
      
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2" for="newPassword">
          New Password
        </label>
        <div class="relative">
          <input
            id="newPassword"
            v-model="passwordForm.newPassword"
            :type="showNewPassword ? 'text' : 'password'"
            class="w-full rounded border border-gray-300 px-4 py-2 text-sm"
            required
          />
          <button
            type="button"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400"
            @click="toggleNewPasswordVisibility"
          >
            <i class="fas" :class="showNewPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
          </button>
        </div>
      </div>
      
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2" for="confirmPassword">
          Confirm New Password
        </label>
        <div class="relative">
          <input
            id="confirmPassword"
            v-model="passwordForm.confirmPassword"
            :type="showConfirmPassword ? 'text' : 'password'"
            class="w-full rounded border border-gray-300 px-4 py-2 text-sm"
            required
          />
          <button
            type="button"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400"
            @click="toggleConfirmPasswordVisibility"
          >
            <i class="fas" :class="showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
          </button>
        </div>
      </div>
      
      <div v-if="error" class="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
        {{ error }}
      </div>
      
      <div v-if="success" class="mb-4 p-3 bg-green-100 text-green-700 rounded-md text-sm">
        {{ success }}
      </div>
      
      <div class="flex justify-end">
        <button 
          type="submit" 
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          :disabled="isSubmitting"
          :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }"
        >
          {{ isSubmitting ? 'Updating...' : 'Update Password' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useStore } from 'vuex';
import api from '@/services/apiService';

const store = useStore();
const currentUser = store.getters['auth/currentUser'];

const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const showCurrentPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);
const error = ref('');
const success = ref('');
const isSubmitting = ref(false);

const toggleCurrentPasswordVisibility = () => {
  showCurrentPassword.value = !showCurrentPassword.value;
};

const toggleNewPasswordVisibility = () => {
  showNewPassword.value = !showNewPassword.value;
};

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

const resetPassword = async () => {
  // Reset messages
  error.value = '';
  success.value = '';
  
  // Validate passwords match
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    error.value = "New passwords don't match";
    return;
  }
  
  // Validate password length
  if (passwordForm.value.newPassword.length < 6) {
    error.value = "New password must be at least 6 characters long";
    return;
  }
  
  isSubmitting.value = true;
  
  try {
    // Get the current user's subsidiary
    const subsidiary = currentUser.sub;
    
    // Call the API to update the password
    const response = await api.put(
      `/user/${currentUser.id}`,
      {
        currentPassword: passwordForm.value.currentPassword,
        password: passwordForm.value.newPassword
      },
      {
        headers: {
          'x-subsidiary': subsidiary,
          'Content-Type': 'application/json'
        }
      }
    );
    
    // Show success message
    success.value = 'Password updated successfully';
    
    // Clear the form
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    };
  } catch (err) {
    console.error('Error updating password:', err);
    error.value = err.response?.data?.message || 'Failed to update password. Please check your current password and try again.';
  } finally {
    isSubmitting.value = false;
  }
};
</script>
