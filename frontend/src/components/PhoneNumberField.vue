<template>
  <div class="flex flex-col">
    <label :for="id" class="font-medium mb-1">{{ label }}</label>
    <div class="flex">
      <div class="relative">
        <select
          v-model="selectedCountryCode"
          class="border rounded-l p-2 bg-gray-50 border-r-0 focus:outline-none focus:ring-1 focus:ring-blue-500"
          @change="updatePhoneNumber"
        >
          <option v-for="code in availableCountryCodes" :key="code.code" :value="code.code">
            {{ code.code }}
          </option>
        </select>
      </div>
      <input
        :id="id"
        type="tel"
        v-model="localPhoneNumber"
        @input="validateAndUpdatePhoneNumber"
        class="border rounded-r p-2 w-full focus:outline-none focus:ring-1 focus:ring-blue-500"
        :placeholder="placeholder"
        required
      />
    </div>
    <div v-if="error" class="text-red-500 text-sm mt-1">
      {{ error }}
    </div>
    <div class="text-gray-500 text-xs mt-1">
      {{ helpText }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    id: String,
    label: String,
    modelValue: String,
    subsidiary: {
      type: String,
      required: true
    }
  },
  emits: ["update:modelValue"],
  data() {
    return {
      selectedCountryCode: "",
      localPhoneNumber: "",
      error: "",
      countryCodes: {
        // Kenya subsidiaries
        platinumkenya: "+254",
        premierkenya: "+254",
        momentumcredit: "+254",

        // Tanzania subsidiaries
        premierfanikiwa: "+255",
        platinumtanzania: "+255",

        // Uganda subsidiaries
        platinumuganda: "+256",
        premieruganda: "+256",

        // Zambia subsidiary
        spectrumzambia: "+260",

        // South Africa subsidiary
        premiersouthafrica: "+27"
      },
      phoneNumberLengths: {
        "+254": 9, // Kenya: 9 digits after country code
        "+255": 9, // Tanzania: 9 digits after country code
        "+256": 9, // Uganda: 9 digits after country code
        "+260": 9, // Zambia: 9 digits after country code
        "+27": 9   // South Africa: 9 digits after country code
      }
    };
  },
  computed: {
    availableCountryCodes() {
      // Return all country codes with the subsidiary's code first
      const allCodes = Object.values(this.countryCodes);
      const uniqueCodes = [...new Set(allCodes)];

      // Sort to put the subsidiary's code first
      return uniqueCodes.map(code => ({ code })).sort((a, b) => {
        if (a.code === this.countryCodes[this.subsidiary]) return -1;
        if (b.code === this.countryCodes[this.subsidiary]) return 1;
        return 0;
      });
    },
    placeholder() {
      const countryCode = this.selectedCountryCode;
      if (countryCode === "+254") return "e.g. 722123456";
      if (countryCode === "+255") return "e.g. 755123456";
      if (countryCode === "+256") return "e.g. 777123456";
      if (countryCode === "+260") return "e.g. 977123456";
      if (countryCode === "+27") return "e.g. 821234567";
      return "Enter phone number";
    },
    helpText() {
      const countryCode = this.selectedCountryCode;
      const length = this.phoneNumberLengths[countryCode] || 9;
      return `Enter ${length} digits after the country code`;
    }
  },
  methods: {
    async validateAndUpdatePhoneNumber() {
      // Remove any non-digit characters
      this.localPhoneNumber = this.localPhoneNumber.replace(/\D/g, "");

      const expectedLength = this.phoneNumberLengths[this.selectedCountryCode] || 9;

      if (this.localPhoneNumber.length > 0 && this.localPhoneNumber.length !== expectedLength) {
        this.error = `Phone number should be ${expectedLength} digits after the country code`;
      } else {
        this.error = "";

        // Check for duplicates if phone number is complete
        if (this.localPhoneNumber.length === expectedLength) {
          await this.checkPhoneNumberDuplicate();
        }
      }

      this.updatePhoneNumber();
    },
    async checkPhoneNumberDuplicate() {
      try {
        const fullNumber = `${this.selectedCountryCode}${this.localPhoneNumber}`;
        const response = await fetch(`http://localhost:7081/user-access/v1/access-request/validate-phone?telephone=${encodeURIComponent(fullNumber)}`, {
          headers: {
            'x-subsidiary': this.subsidiary,
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.exists) {
            this.error = `Phone number ${fullNumber} is already registered in the system.`;
          }
        }
      } catch (error) {
        console.error('Error checking phone number duplicate:', error);
        // Don't show error to user for network issues
      }
    },
    updatePhoneNumber() {
      // Combine country code and phone number
      const fullNumber = this.localPhoneNumber ?
        `${this.selectedCountryCode}${this.localPhoneNumber}` :
        "";

      this.$emit("update:modelValue", fullNumber);
    },
    parsePhoneNumber(value) {
      if (!value) return { countryCode: "", number: "" };

      // Try to match a country code at the beginning
      for (const code of Object.values(this.countryCodes)) {
        if (value.startsWith(code)) {
          return {
            countryCode: code,
            number: value.substring(code.length)
          };
        }
      }

      // If no country code found, use the subsidiary's default
      return {
        countryCode: this.countryCodes[this.subsidiary] || "",
        number: value
      };
    }
  },
  created() {
    // Set default country code based on subsidiary
    this.selectedCountryCode = this.countryCodes[this.subsidiary] || "+254";

    // Parse existing value if any
    if (this.modelValue) {
      const { countryCode, number } = this.parsePhoneNumber(this.modelValue);
      this.selectedCountryCode = countryCode || this.selectedCountryCode;
      this.localPhoneNumber = number;
    }
  },
  watch: {
    modelValue(newValue) {
      if (newValue !== `${this.selectedCountryCode}${this.localPhoneNumber}`) {
        const { countryCode, number } = this.parsePhoneNumber(newValue);
        this.selectedCountryCode = countryCode || this.selectedCountryCode;
        this.localPhoneNumber = number;
      }
    },
    subsidiary(newValue) {
      // Update country code when subsidiary changes
      this.selectedCountryCode = this.countryCodes[newValue] || this.selectedCountryCode;
    }
  }
};
</script>
