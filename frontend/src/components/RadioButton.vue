<template>
  <div class="flex items-center space-x-2">
    <input
      :id="id"
      type="radio"
      :value="value"
      :checked="modelValue === value"
      @change="$emit('update:modelValue', value)"
      class="cursor-pointer"
    />
    <label :for="id" class="cursor-pointer">{{ label }}</label>
  </div>
</template>

<script>
export default {
  props: {
    id: String,
    label: String,
    value: String,
    modelValue: String,
  },
  emits: ["update:modelValue"],
};
</script>
