<template>
  <div class="flex flex-col">
    <label :for="id" class="font-medium mb-1">{{ label }}</label>
    <select
      :id="id"
      :value="modelValue"
      @change="$emit('update:modelValue', $event.target.value)"
      class="border rounded p-2 w-full"
      required
    >
      <option v-for="option in options" :key="option" :value="option">
        {{ option }}
      </option>
    </select>
  </div>
</template>

<script>
export default {
  props: {
    id: String,
    label: String,
    options: Array,
    modelValue: String,
  },
  emits: ["update:modelValue"],
};
</script>
