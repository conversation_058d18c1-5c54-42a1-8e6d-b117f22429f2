<template>
  <div class="flex flex-col">
    <label :for="id" class="font-medium mb-1">{{ label }}</label>
    <input
      :id="id"
      :type="type"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      class="border rounded p-2 w-full"
      required
    />
  </div>
</template>

<script>
export default {
  props: {
    id: String,
    label: String,
    type: {
      type: String,
      default: "text",
    },
    modelValue: String,
  },
  emits: ["update:modelValue"],
};
</script>
