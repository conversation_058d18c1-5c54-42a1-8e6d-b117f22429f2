<template>
  <form @submit.prevent="onSubmit">
    <!-- Form Error -->
    <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      {{ error }}
    </div>

    <!-- Name -->
    <div class="mb-4">
      <label class="block text-sm mb-2 text-gray-700 font-medium" for="name">
        Tenant Name
      </label>
      <input
        id="name"
        v-model="name"
        type="text"
        class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
        required
        placeholder="e.g. Platinum Kenya Credit IT Department"
      />
    </div>

    <!-- Subsidiary Code -->
    <div class="mb-4">
      <label class="block text-sm mb-2 text-gray-700 font-medium" for="sub">
        Subsidiary Code
      </label>
      <input
        id="sub"
        v-model="sub"
        type="text"
        class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
        required
        placeholder="e.g. platinumkenya"
        :disabled="isEditing"
      />
      <p v-if="isEditing" class="text-xs text-gray-500 mt-1">
        Subsidiary code cannot be changed after creation
      </p>
    </div>

    <!-- URL -->
    <div class="mb-4">
      <label class="block text-sm mb-2 text-gray-700 font-medium" for="url">
        URL
      </label>
      <input
        id="url"
        v-model="url"
        type="url"
        class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
        required
        placeholder="e.g. https://platinumcredit.co.ke"
      />
    </div>

    <!-- Matcher -->
    <div class="mb-4">
      <label class="block text-sm mb-2 text-gray-700 font-medium" for="matcher">
        Domain Matcher
      </label>
      <input
        id="matcher"
        v-model="matcher"
        type="text"
        class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
        placeholder="e.g. platinumcredit.co.ke"
      />
      <p class="text-xs text-gray-500 mt-1">
        Used to match email domains to this tenant (optional)
      </p>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-center w-full pt-6">
      <button
        type="submit"
        class="w-1/2 py-3 rounded bg-black text-white flex items-center justify-center text-base font-medium"
        :disabled="submitting"
      >
        <span v-if="submitting">
          <i class="fas fa-spinner fa-spin mr-2"></i> Saving...
        </span>
        <span v-else class="flex items-center justify-center">
          <i class="fas fa-building mr-2"></i>
          {{ isEditing ? 'Update Tenant' : 'Create Tenant' }}
        </span>
      </button>
    </div>
  </form>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';

const props = defineProps({
  tenant: {
    type: Object,
    default: null
  },
  error: {
    type: String,
    default: null
  },
  submitting: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['submit']);

// Individual form fields
const name = ref('');
const sub = ref('');
const url = ref('');
const matcher = ref('');

// Computed property to check if we're editing
const isEditing = computed(() => !!props.tenant);

// Handle form submission
const onSubmit = () => {
  // Validate form data
  if (!name.value || !sub.value || !url.value) {
    console.error('Form validation failed: Missing required fields');
    return;
  }

  // Create form data object with all required fields
  const formData = {
    name: name.value,
    sub: sub.value,
    url: url.value,
    matcher: matcher.value
  };

  // Emit the form data to parent component
  emit('submit', formData);
};

// Initialize form when component is mounted
onMounted(() => {
  if (props.tenant) {
    // If we have a tenant, set the form values
    const tenant = props.tenant;

    // Set the form values
    name.value = tenant.name || '';
    sub.value = tenant.sub || '';
    url.value = tenant.url || '';
    matcher.value = tenant.matcher || '';

    console.log('Form values set on mount:', {
      name: name.value,
      sub: sub.value,
      url: url.value,
      matcher: matcher.value
    });

    // Force update the DOM elements
    nextTick(() => {
      // Directly set form field values using DOM manipulation
      const nameInput = document.getElementById('name');
      const subInput = document.getElementById('sub');
      const urlInput = document.getElementById('url');
      const matcherInput = document.getElementById('matcher');

      if (nameInput) nameInput.value = tenant.name || '';
      if (subInput) subInput.value = tenant.sub || '';
      if (urlInput) urlInput.value = tenant.url || '';
      if (matcherInput) matcherInput.value = tenant.matcher || '';

      console.log('Form fields set directly via DOM on mount');
    });
  }
});

// Watch for changes in the tenant prop
watch(() => props.tenant, (newTenant) => {
  if (newTenant) {
    name.value = newTenant.name || '';
    sub.value = newTenant.sub || '';
    url.value = newTenant.url || '';
    matcher.value = newTenant.matcher || '';

    console.log('Form values updated on tenant change:', {
      name: name.value,
      sub: sub.value,
      url: url.value,
      matcher: matcher.value
    });
  } else {
    // Reset form if tenant is null
    name.value = '';
    sub.value = '';
    url.value = '';
    matcher.value = '';
  }
}, { deep: true });
</script>
