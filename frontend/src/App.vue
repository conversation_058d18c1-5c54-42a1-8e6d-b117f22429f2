<template>
  <div class="min-h-screen bg-gray-50 flex">
    <!-- Password Reset Overlay - shown if user needs to change password and not on login page -->
    <div v-if="requiresPasswordChange && isAuthenticated && !isAuthPage" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">Password Reset Required</h2>
        <p class="mb-4">For security reasons, you need to change your temporary password before accessing the system.</p>
        <button
          @click="goToPasswordReset"
          class="w-full py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Continue to Password Reset
        </button>
      </div>
    </div>

    <!-- Sidebar - hidden on mobile by default, shown on desktop if authenticated and not on auth page -->
    <MainSidebar
      v-if="!isAuthPage && isAuthenticated && !requiresPasswordChange"
      :subsidiary="subsidiary"
      class="hidden md:block fixed inset-y-0 left-0 z-20 w-64 transition-transform duration-300 ease-in-out"
    />

    <!-- Main Content Area - takes full width on mobile, adjusted width on desktop -->
    <div
      class="flex-1 flex flex-col min-w-0 transition-all duration-300 ease-in-out"
      :class="{
        'md:pl-64': isAuthenticated && !isAuthPage && !requiresPasswordChange
      }"
    >
      <!-- Navbar - shown if authenticated and not on auth page -->
      <MainNavbar
        v-if="!isAuthPage && isAuthenticated && !requiresPasswordChange"
        :subsidiary="subsidiary"
        class="sticky top-0 z-10 w-full"
      />

      <!-- Page Content -->
      <main class="flex-1 relative overflow-y-auto focus:outline-none">
        <div class="py-6 px-4 sm:px-6 lg:px-8">
          <!-- Router View -->
          <router-view :subsidiary="subsidiary" />
        </div>
      </main>

      <!-- Footer - optional -->
      <footer v-if="isAuthenticated && !isAuthPage && !requiresPasswordChange" class="bg-white border-t border-gray-200 p-4 text-center text-sm text-gray-500">
        <p>&copy; {{ new Date().getFullYear() }} Digital User Access System</p>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import MainNavbar from "@/components/MainNavbar.vue";
import MainSidebar from "@/components/MainSidebar.vue";

// Get route, router, and store instances
const route = useRoute();
const router = useRouter();
const store = useStore();

// Computed properties
const isAuthenticated = computed(() => store.getters['auth/isAuthenticated']);
const currentUser = computed(() => store.getters['auth/currentUser']);

const requiresPasswordChange = computed(() => {
  const user = store.getters['auth/currentUser'];
  const needsPasswordChange = user?.requirePasswordChange === true;
  console.log('App.vue - requiresPasswordChange:', needsPasswordChange, 'user:', user);
  return needsPasswordChange;
});

const subsidiary = computed(() => {
  // Get the user's subsidiary from the store
  const userSubsidiary = store.getters['auth/currentUser']?.sub;

  // If the user is logged in and has a subsidiary, use that
  // Otherwise, use the route parameter or default to platinumkenya
  return userSubsidiary || route.params.subsidiary || "platinumkenya";
});

const isAuthPage = computed(() => {
  // Check if current route is an authentication page
  return (
    route.path === "/" ||
    route.path === "/user-access/ui/" ||
    route.path === "/login" ||
    route.path === "/password-reset" ||
    route.path === "/user-access/ui/password-reset"
  );
});

// Methods
const goToPasswordReset = () => {
  router.push('/password-reset');
};

// Watch for changes in authentication state
watch(isAuthenticated, (newValue) => {
  if (newValue && requiresPasswordChange.value) {
    console.log('User authenticated and needs to change password');
    if (!isAuthPage.value && route.path !== '/password-reset' && route.path !== '/user-access/ui/password-reset') {
      goToPasswordReset();
    }
  }
});

// Watch for changes in the current user
watch(currentUser, (newUser) => {
  if (newUser && newUser.requirePasswordChange) {
    console.log('Current user updated and needs to change password');
    if (!isAuthPage.value && route.path !== '/password-reset' && route.path !== '/user-access/ui/password-reset') {
      goToPasswordReset();
    }
  }
});

// Check on mount
onMounted(() => {
  if (isAuthenticated.value && requiresPasswordChange.value) {
    console.log('App mounted - User needs to change password');
    if (!isAuthPage.value && route.path !== '/password-reset' && route.path !== '/user-access/ui/password-reset') {
      goToPasswordReset();
    }
  }
});
</script>

<style>
/* Custom styles */

/* Button styles */
.btn-primary {
  padding: 0.5rem 1rem; /* px-4 py-2 */
  background-color: #2563eb; /* bg-blue-600 */
  color: white;
  border-radius: 0.375rem; /* rounded-md */
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #1d4ed8; /* bg-blue-700 */
}

/* Card styles */
.card {
  background-color: white; /* bg-white */
  border-radius: 0.5rem; /* rounded-lg */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-md */
  padding: 1.5rem; /* p-6 */
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f3f4f6; /* bg-gray-100 */
}

::-webkit-scrollbar-thumb {
  background-color: #9ca3af; /* bg-gray-400 */
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280; /* bg-gray-500 */
}
</style>
