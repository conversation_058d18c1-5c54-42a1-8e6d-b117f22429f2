<template>
  <div class="container">
    <!-- Notification -->
    <div v-if="notification.show"
      class="fixed top-4 right-4 px-4 py-3 rounded z-50 transition-opacity duration-300"
      :class="{
        'bg-green-100 border border-green-400 text-green-700': notification.type === 'success',
        'bg-red-100 border border-red-400 text-red-700': notification.type === 'error',
        'bg-blue-100 border border-blue-400 text-blue-700': notification.type === 'info',
        'opacity-100': notification.show,
        'opacity-0': !notification.show
      }"
    >
      <strong class="font-bold">{{ notification.title }}</strong>
      <span class="block sm:inline"> {{ notification.message }}</span>
      <button @click="notification.show = false" class="ml-2 text-gray-500 hover:text-gray-700">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="bg-white rounded-lg shadow-md p-20">
      <div class="mb-2">
        <h1 class="text-xl font-bold text-orange-600">
          User Management
        </h1>
        <p class="text-gray-500 text-sm font-medium">Manage system users and their permissions</p>
      </div>

      <div class="flex flex-wrap mb-6">
        <div class="flex mb-2 sm:mb-0">
          <button
            @click="fetchUsers"
            class="flex items-center justify-center bg-blue-500 text-white px-4 py-2 rounded-md mr-2"
          >
            <i class="fas fa-sync-alt mr-2"></i> Refresh
          </button>

          <button
            v-if="canCreateUsers"
            @click="createNewUser"
            class="flex items-center justify-center bg-green-500 text-white px-4 py-2 rounded-md mr-2"
          >
            <i class="fas fa-plus mr-2"></i> Create User
          </button>

          <button
            v-if="canCreateUsers"
            @click="showBulkUploadModal = true"
            class="flex items-center justify-center bg-purple-500 text-white px-4 py-2 rounded-md mr-2"
          >
            <i class="fas fa-upload mr-2"></i> Bulk Upload
          </button>

          <!-- Filter toggle button -->
          <button
            @click="toggleShowInactive"
            class="flex items-center justify-center px-4 py-2 rounded-md"
            :class="showInactive ? 'bg-gray-500 text-white' : 'bg-yellow-500 text-white'"
          >
            <i class="fas fa-filter mr-2"></i>
            {{ showInactive ? 'Show Active Only' : 'Show All Users' }}
          </button>
        </div>

        <!-- Search input -->
        <div class="flex items-center ml-auto mt-2 sm:mt-0">
          <div class="relative">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="Search users..."
              class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
            />
            <div class="absolute left-3 top-2.5 text-gray-400">
              <i class="fas fa-search"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-10">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline"> {{ error }}</span>
      </div>

      <!-- User Table -->
      <div v-else-if="users.length > 0" class="overflow-x-auto">
        <!-- Search results indicator -->
        <div v-if="searchQuery" class="mb-2 text-sm text-gray-600">
          Showing {{ filteredUsers.length }} {{ filteredUsers.length === 1 ? 'result' : 'results' }} for "{{ searchQuery }}"
          <button
            @click="clearSearch"
            class="ml-2 text-blue-500 hover:text-blue-700"
          >
            <i class="fas fa-times-circle"></i> Clear search
          </button>
        </div>

        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                NAME
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                EMAIL
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                ROLE
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STATUS</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ACTIONS</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in paginatedUsers" :key="user.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">{{ formatUsername(user.username) }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ user.email }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs rounded-md bg-blue-100 text-blue-800"
                >
                  {{ user.role }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs rounded-md"
                  :class="{
                    'bg-green-100 text-green-800': user.status === 'active',
                    'bg-red-100 text-red-800': user.status === 'inactive'
                  }"
                >
                  {{ user.status || 'active' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-6">
                  <button
                    v-if="canEditUsers"
                    @click="editUser(user)"
                    class="text-blue-600"
                    title="Edit User"
                  >
                    <span class="flex items-center">
                      <i class="fas fa-edit mr-1"></i> Edit
                    </span>
                  </button>
                  <button
                    v-if="canChangePassword"
                    @click="openPasswordModal(user)"
                    class="text-blue-600"
                    title="Change Password"
                  >
                    <span class="flex items-center">
                      <i class="fas fa-key mr-1"></i> Change Password
                    </span>
                  </button>
                  <button
                    v-if="canDeleteUsers"
                    @click="confirmDelete(user)"
                    class="text-red-600"
                    title="Delete User"
                  >
                    <span class="flex items-center">
                      <i class="fas fa-trash mr-1"></i> Delete
                    </span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Pagination -->
        <div class="mt-4 flex justify-end items-center text-sm">
          <div class="text-gray-600 flex items-center">
            <span>
              {{ searchQuery ? `Showing ${filteredUsers.length} of ${users.length} users` : `Total: ${users.length} users` }}
            </span>
            <div class="mx-2 flex space-x-1">
              <button
                v-for="page in totalPages"
                :key="page"
                @click="currentPage = page"
                class="border border-gray-300 rounded-md px-2 py-1"
                :class="{ 'bg-blue-500 text-white': currentPage === page }"
              >
                {{ page }}
              </button>
            </div>
            <span>{{ currentPage }} / {{ totalPages }} pages</span>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-10">
        <i class="fas fa-users text-gray-300 text-5xl mb-4"></i>
        <p class="text-gray-500">No users found. Click "Create User" to create one.</p>
      </div>
    </div>

    <!-- User Modal -->
    <div v-if="showUserModal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="bg-white rounded shadow-md p-6 w-full max-w-2xl relative">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-base font-medium text-gray-800">
            {{ editingUser ? 'Edit User' : 'Create New User' }}
          </h2>
          <button @click="closeUserModal" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- Use the new UserForm component -->
        <UserForm
          :key="editingUser ? `edit-form-${editingUser.id}` : `create-form-${formId}`"
          :user="editingUser"
          :error="formError"
          :submitting="formSubmitting"
          @submit="saveUser"
        />
      </div>
    </div>

    <!-- Password Change Modal -->
    <div v-if="showPasswordModal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="bg-white rounded shadow-md p-6 w-full max-w-md relative">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-base font-medium text-gray-800">
            Change Password for {{ formatUsername(userToChangePassword?.username) }}
          </h2>
          <button @click="showPasswordModal = false" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <form @submit.prevent="changePassword">
          <div class="mb-4">
            <label class="block text-sm mb-1 text-gray-700" for="newPassword">
              <span class="text-red-500 mr-1">*</span> New Password
            </label>
            <div class="relative">
              <input
                id="newPassword"
                v-model="passwordForm.newPassword"
                :type="showNewPassword ? 'text' : 'password'"
                class="w-full rounded border border-gray-300 px-3 py-2 text-sm bg-blue-50"
                required
              />
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400"
                @click="toggleNewPasswordVisibility"
              >
                <i class="fas" :class="showNewPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
              </button>
            </div>
          </div>

          <div class="mb-4">
            <label class="block text-sm mb-1 text-gray-700" for="confirmPassword">
              <span class="text-red-500 mr-1">*</span> Confirm Password
            </label>
            <div class="relative">
              <input
                id="confirmPassword"
                v-model="passwordForm.confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                class="w-full rounded border border-gray-300 px-3 py-2 text-sm bg-blue-50"
                required
              />
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400"
                @click="toggleConfirmPasswordVisibility"
              >
                <i class="fas" :class="showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
              </button>
            </div>
          </div>

          <!-- Password Error -->
          <div v-if="passwordError" class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
            {{ passwordError }}
          </div>

          <div class="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              class="px-4 py-2 border border-gray-300 rounded bg-white text-gray-700"
              @click="showPasswordModal = false"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 rounded bg-black text-white"
              :disabled="passwordSubmitting"
            >
              <span v-if="passwordSubmitting">
                <i class="fas fa-spinner fa-spin mr-2"></i> Updating...
              </span>
              <span v-else>Update Password</span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="bg-white rounded shadow-md p-6 w-full max-w-md relative">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-base font-medium text-gray-800">
            Confirm Deletion
          </h2>
          <button @click="showDeleteModal = false" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <p class="mb-6 text-gray-600">
          Are you sure you want to permanently delete the user <strong>{{ formatUsername(userToDelete?.username) }}</strong>?
          This action cannot be undone.
        </p>

        <div class="flex justify-end space-x-4">
          <button
            type="button"
            class="px-4 py-2 border border-gray-300 rounded bg-white text-gray-700"
            @click="showDeleteModal = false"
          >
            Cancel
          </button>
          <button
            type="button"
            class="px-4 py-2 rounded bg-red-600 text-white"
            @click="deleteUserConfirmed"
            :disabled="deleteSubmitting"
          >
            <span v-if="deleteSubmitting">
              <i class="fas fa-spinner fa-spin mr-2"></i> Deleting...
            </span>
            <span v-else>Delete</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Bulk Upload Modal -->
    <div v-if="showBulkUploadModal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="fixed inset-0" @click="closeBulkUploadModal"></div>
      <div class="relative z-10 bg-white rounded-lg shadow-lg w-full max-w-md">
        <div class="flex justify-between items-center p-4 border-b">
          <h2 class="text-lg font-medium">Bulk User Upload</h2>
          <button @click="closeBulkUploadModal" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="p-4">
          <div class="mb-4">
            <p class="text-sm text-gray-600 mb-4">
              Upload a CSV file with user information. The file should have the following columns:
              <span class="font-semibold">FirstName, LastName, Email, Password, UserRole, AccountState</span>.
            </p>

            <div class="mb-4 bg-blue-50 p-3 rounded-md">
              <h4 class="font-medium text-blue-800 mb-1">Valid values:</h4>
              <ul class="text-sm text-blue-700 list-disc pl-5">
                <li><span class="font-semibold">UserRole:</span> systemAdmin, hr, it, supervisor</li>
                <li><span class="font-semibold">AccountState:</span> active, inactive</li>
              </ul>
            </div>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Upload CSV File
            </label>
            <div class="flex items-center">
              <label
                class="cursor-pointer px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
              >
                <span>{{ selectedFile ? selectedFile.name : 'Select File' }}</span>
                <input
                  type="file"
                  class="hidden"
                  accept=".csv"
                  @change="handleFileUpload"
                />
              </label>
              <button
                @click="downloadTemplate"
                class="ml-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
              >
                Download Template
              </button>
            </div>
          </div>

          <div class="flex justify-end">
            <button
              @click="uploadUsers"
              class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 focus:outline-none"
              :disabled="!selectedFile"
              :class="{ 'opacity-50 cursor-not-allowed': !selectedFile }"
            >
              Upload Users
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import api, { getAllUsers, createUser, updateUser, deleteUser } from '@/services/apiService';
import { subsidiaries } from '@/config/subsidiaries';
import { canAccess } from '@/utils/accessControl';
import Papa from 'papaparse';

// Import components
import { Button } from '@/components/ui/button';
import UserForm from '@/components/UserForm.vue';
import BulkUserUpload from '@/components/BulkUserUpload.vue';

// Props
const props = defineProps(['subsidiary']);

// Store and router
const store = useStore();
const router = useRouter();

// Check if user has permission to access this page
const currentUser = store.getters['auth/currentUser'];
const hasPermission = canAccess('users', currentUser);

if (!hasPermission) {
  console.warn(`User ${currentUser?.username} with role ${currentUser?.role} does not have permission to access User Management`);
  // Redirect to dashboard if user doesn't have permission
  router.push(`/dashboard/${props.subsidiary}`);
}

// State
const users = ref([]);
const loading = ref(true);
const error = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(10);
const searchQuery = ref('');
const formId = ref(0); // Unique ID for form resets
const showInactive = ref(false); // Show all users by default (including inactive)

// User modal state
const showUserModal = ref(false);
const editingUser = ref(null);
const userForm = ref({
  firstName: '',
  lastName: '',
  username: '',
  email: '',
  password: '',
  role: '', // Empty to show placeholder
  status: '' // Empty to show placeholder
});
const formError = ref(null);
const formSubmitting = ref(false);
const showPassword = ref(false);

// Toggle password visibility
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
  const passwordInput = document.getElementById('password');
  if (passwordInput) {
    passwordInput.type = showPassword.value ? 'text' : 'password';
  }
};

// Delete modal state
const showDeleteModal = ref(false);
const userToDelete = ref(null);
const deleteSubmitting = ref(false);

// Password change modal state
const showPasswordModal = ref(false);
const userToChangePassword = ref(null);
const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
});
const passwordError = ref(null);
const passwordSubmitting = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// Bulk upload modal state
const showBulkUploadModal = ref(false);
const selectedFile = ref(null);

// Toggle password visibility for password change modal
const toggleNewPasswordVisibility = () => {
  showNewPassword.value = !showNewPassword.value;
};

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

// Notification state
const notification = ref({
  show: false,
  type: 'info', // 'success', 'error', 'info'
  title: '',
  message: '',
  timeout: null
});

// Theme based on subsidiary
const theme = computed(() => {
  return subsidiaries[props.subsidiary] || {
    primaryColor: '#4F46E5',
    secondaryColor: '#818CF8',
    accentColor: '#C7D2FE'
  };
});

// Toggle showing inactive users
const toggleShowInactive = () => {
  showInactive.value = !showInactive.value;
  console.log(`Toggled showInactive to: ${showInactive.value}`);

  // Force refresh the user list to ensure we see all users
  fetchUsers();
};

// Filtered users based on search query and active status
const filteredUsers = computed(() => {
  // First filter by status if needed
  let statusFilteredUsers = users.value;
  if (showInactive.value) {
    // Only show active users
    statusFilteredUsers = users.value.filter(user => user.status === 'active');
    console.log(`Filtered out ${users.value.length - statusFilteredUsers.length} inactive users`);
  } else {
    // Show all users (both active and inactive)
    console.log(`Showing all ${users.value.length} users, including inactive ones`);
  }

  // Then filter by search query if provided
  if (!searchQuery.value) return statusFilteredUsers;

  const query = searchQuery.value.toLowerCase();
  return statusFilteredUsers.filter(user => {
    // Extract first and last name from username (format: "FirstName;LastName")
    let firstName = '';
    let lastName = '';

    if (user.username && user.username.includes(';')) {
      const nameParts = user.username.split(';');
      firstName = nameParts[0]?.toLowerCase() || '';
      lastName = nameParts[1]?.toLowerCase() || '';
    }

    // Search in all relevant fields
    return (
      user.username?.toLowerCase().includes(query) ||
      user.email?.toLowerCase().includes(query) ||
      user.role?.toLowerCase().includes(query) ||
      firstName.includes(query) ||
      lastName.includes(query)
    );
  });
});

// Computed properties for pagination
const totalPages = computed(() => {
  return Math.ceil(filteredUsers.value.length / itemsPerPage.value);
});

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredUsers.value.slice(start, end);
});

// Computed property for edit mode
const editMode = computed(() => {
  return !!editingUser.value;
});

// Role-based access control computed properties
const canCreateUsers = computed(() => {
  return canAccess('users-create', currentUser);
});

const canEditUsers = computed(() => {
  return canAccess('users-edit', currentUser);
});

const canDeleteUsers = computed(() => {
  return canAccess('users-delete', currentUser);
});

const canChangePassword = computed(() => {
  return canAccess('users-change-password', currentUser);
});

// Clear search query
const clearSearch = () => {
  searchQuery.value = '';
  // Reset to first page when clearing search
  currentPage.value = 1;
};

// Format username to display without semicolon
const formatUsername = (username) => {
  if (!username) return '';

  // If username contains a semicolon, replace it with a space
  if (username.includes(';')) {
    // Split by semicolon and join with space
    const parts = username.split(';').map(part => part.trim());
    return parts.join(' ');
  }

  return username;
};

// Methods
const showNotification = (type, title, message, duration = 3000) => {
  // Clear any existing timeout
  if (notification.value.timeout) {
    clearTimeout(notification.value.timeout);
  }

  // Update notification
  notification.value = {
    show: true,
    type,
    title,
    message,
    timeout: null
  };

  // Auto-hide after duration
  notification.value.timeout = setTimeout(() => {
    notification.value.show = false;
  }, duration);
};

const fetchUsers = async () => {
  loading.value = true;
  error.value = null;

  try {
    console.log('Fetching users for subsidiary:', props.subsidiary);

    // Make a direct API call to ensure we get the latest data
    const response = await api.get('/user', {
      headers: {
        'x-subsidiary': props.subsidiary,
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    console.log('Direct API response:', response);

    // Extract users from the response
    let fetchedUsers = [];

    if (response && response.data) {
      const result = response.data;
      console.log('Users data from API:', result);
      console.log('Users data structure:', {
        hasData: !!result,
        dataType: typeof result,
        isArray: Array.isArray(result),
        hasUsers: result.users ? true : false,
        hasDataProperty: result.data ? true : false,
        hasDataUsers: result.data?.users ? true : false,
        keys: Object.keys(result)
      });

      // Try all possible response formats
      if (result && result.data && Array.isArray(result.data.users)) {
        console.log('Format 1: result.data.users');
        fetchedUsers = result.data.users;
      } else if (result && result.data && Array.isArray(result.data)) {
        console.log('Format 2: result.data');
        fetchedUsers = result.data;
      } else if (result && Array.isArray(result.users)) {
        console.log('Format 3: result.users');
        fetchedUsers = result.users;
      } else if (result && Array.isArray(result)) {
        console.log('Format 4: result');
        fetchedUsers = result;
      } else {
        console.log('No users found in response or unexpected format');
        fetchedUsers = [];
      }
    } else {
      console.log('No data in API response');
      fetchedUsers = [];
    }

    console.log('Extracted users:', fetchedUsers);

    // Show all users including inactive ones
    console.log('Showing all users including inactive ones');

    // Only do basic validation to ensure we have valid user objects
    fetchedUsers = fetchedUsers.filter(user => {
      // Skip null or undefined users
      if (!user) {
        console.log('Filtering out null/undefined user');
        return false;
      }

      // Skip users that were hard deleted in this session
      if (window.deletedUserIds && window.deletedUserIds.has(user.id)) {
        console.log(`Filtering out previously deleted user by ID: ${user.id}`);
        return false;
      }

      // Skip users that were hard deleted in this session (by email as backup)
      if (window.deletedUserEmails && user.email && window.deletedUserEmails.has(user.email)) {
        console.log(`Filtering out previously deleted user by email: ${user.email}`);
        return false;
      }

      // Include all users regardless of status
      return true;
    });

    console.log('Users after filtering:', fetchedUsers.length);

    // Update the users array
    users.value = fetchedUsers;

    console.log('Updated users array after filtering:', users.value);
  } catch (err) {
    console.error('Error fetching users:', err);
    error.value = err.message || 'Failed to load users';
    users.value = [];
  } finally {
    loading.value = false;
  }
};

// Function specifically for creating a new user with empty fields
const createNewUser = () => {
  console.log('Creating new user - resetting form');

  // Check if user has permission to create users
  if (!canCreateUsers.value) {
    showNotification('error', 'Access Denied', 'You do not have permission to create users');
    return;
  }

  // Set editing user to null to indicate we're creating a new user
  editingUser.value = null;

  // Increment form ID to force Vue to recreate the form component
  formId.value++;

  // Clear any previous errors
  formError.value = null;

  // Show the modal
  showUserModal.value = true;
};

// Function specifically for editing an existing user
const editUser = (user) => {
  console.log('Editing user with direct approach:', user);

  // Check if user has permission to edit users
  if (!canEditUsers.value) {
    showNotification('error', 'Access Denied', 'You do not have permission to edit users');
    return;
  }

  if (!user) {
    console.error('No user provided to edit');
    return;
  }

  // Extract first and last name from username
  let firstName = '';
  let lastName = '';

  if (user.username) {
    if (user.username.includes(';')) {
      const nameParts = user.username.split(';');
      firstName = nameParts[0].trim();
      lastName = nameParts.length > 1 ? nameParts[1].trim() : '';
    } else {
      // If username doesn't contain a semicolon, use it as the first name
      firstName = user.username;
    }

    console.log('Extracted name parts for editing:', { firstName, lastName });
  }

  // Create a complete user object with all fields
  const completeUser = {
    ...user,
    firstName,
    lastName
  };

  console.log('Complete user object for editing:', completeUser);

  // Set the editing user
  editingUser.value = completeUser;

  // Show the modal
  showUserModal.value = true;

  // Use nextTick to ensure the DOM is updated before we try to set field values
  nextTick(() => {
    // Directly set form field values using DOM manipulation
    const firstNameInput = document.getElementById('firstName');
    const lastNameInput = document.getElementById('lastName');
    const emailInput = document.getElementById('email');
    const roleSelect = document.getElementById('role');
    const statusSelect = document.getElementById('status');

    console.log('Form elements:', {
      firstNameInput,
      lastNameInput,
      emailInput,
      roleSelect,
      statusSelect
    });

    if (firstNameInput) firstNameInput.value = firstName;
    if (lastNameInput) lastNameInput.value = lastName;
    if (emailInput) emailInput.value = user.email || '';

    if (roleSelect) {
      // Find the option that matches the role
      const roleOptions = Array.from(roleSelect.options);
      console.log('Role options:', roleOptions.map(o => o.value));

      // Set the value directly
      roleSelect.value = user.role || 'systemAdmin';

      // Force a change event to update the v-model
      roleSelect.dispatchEvent(new Event('change', { bubbles: true }));
    }

    if (statusSelect) {
      // Find the option that matches the status
      const statusOptions = Array.from(statusSelect.options);
      console.log('Status options:', statusOptions.map(o => o.value));

      // Set the value directly
      statusSelect.value = user.status || 'active';

      // Force a change event to update the v-model
      statusSelect.dispatchEvent(new Event('change', { bubbles: true }));
    }

    console.log('Form fields set directly via DOM');

    // Force a second update after a short delay to ensure everything is set
    setTimeout(() => {
      if (firstNameInput) firstNameInput.value = firstName;
      if (lastNameInput) lastNameInput.value = lastName;
      if (emailInput) emailInput.value = user.email || '';
      if (roleSelect) roleSelect.value = user.role || 'systemAdmin';
      if (statusSelect) statusSelect.value = user.status || 'active';

      console.log('Form fields set again after delay');
    }, 100);
  });
};

const openUserModal = (user) => {
  // This function is now only used for editing existing users
  if (!user) {
    // If no user is provided, call createNewUser instead
    return createNewUser();
  }

  console.log('Opening user modal for editing:', user);

  // Reset password visibility
  showPassword.value = false;

  // Extract first and last name from username
  let firstName = '';
  let lastName = '';

  if (user.username) {
    if (user.username.includes(';')) {
      const nameParts = user.username.split(';');
      firstName = nameParts[0].trim();
      lastName = nameParts.length > 1 ? nameParts[1].trim() : '';
    } else {
      // If username doesn't contain a semicolon, use it as the first name
      firstName = user.username;
    }
  }

  console.log('Extracted name parts:', { firstName, lastName });

  // Create a new object with all the user data plus the extracted name parts
  const userWithNames = {
    ...user,
    firstName,
    lastName
  };

  console.log('User data with names:', userWithNames);

  // Set the editing user
  editingUser.value = userWithNames;

  // Force a re-render of the form by incrementing the formId
  formId.value++;

  // Clear any previous errors
  formError.value = null;

  // Show the modal
  showUserModal.value = true;

  // Log the current editing user for debugging
  console.log('Current editing user:', editingUser.value);
};

const closeUserModal = () => {
  showUserModal.value = false;
  formError.value = null;

  // Reset form when closing modal
  userForm.value = {
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    password: '',
    role: '',
    status: ''
  };

  // Reset editing user
  editingUser.value = null;
};

const saveUser = async (formData) => {
  formSubmitting.value = true;
  formError.value = null;

  try {
    let result;

    if (editingUser.value) {
      // Handle user update
      result = await handleUserUpdate(formData, editingUser.value);
    } else {
      // Handle user creation
      result = await handleUserCreation(formData);
    }

    // Always set formSubmitting to false immediately after getting a response
    formSubmitting.value = false;

    // Show success notification
    const successMessage = editingUser.value
      ? `User ${formData.email} updated successfully`
      : `User ${formData.email} created successfully`;

    showNotification('success', 'Success!', successMessage);

    // Close the modal
    closeUserModal();

    // Refresh the user list after a short delay
    setTimeout(async () => {
      await fetchUsers();
    }, 1000);

    return result;
  } catch (err) {
    console.error('Error saving user:', err);
    formError.value = err.response?.data?.message || err.message || 'Failed to save user';
    showNotification('error', 'Error!', formError.value);

    // Ensure formSubmitting is set to false even on error
    formSubmitting.value = false;
    throw err;
  }
};

// Function to handle user creation
const handleUserCreation = async (formData) => {
  console.log('Creating new user with form data:', formData);
  console.log('Role from form data:', formData.role);
  console.log('Type of role:', typeof formData.role);

  // Ensure role is a valid value
  if (!formData.role || formData.role === '') {
    console.error('Role is empty or undefined, this will cause issues!');
  }

  try {
    // Validate the role is present and valid
    if (!formData.role || formData.role === '') {
      console.error('Role is missing or empty, cannot create user!');
      showNotification('error', 'Error', 'Role is required. Please select a valid role.');
      return;
    }

    // Force the role to be a specific value for testing
    const forcedRole = formData.role === 'supervisor' ? 'supervisor' : formData.role;
    console.log('Using forced role:', forcedRole);

    // Try using the auth/register endpoint instead of the user endpoint
    // This endpoint doesn't use the audit trail middleware
    const registerData = {
      email: formData.email,
      password: formData.password,
      username: `${formData.firstName};${formData.lastName}`,
      role: forcedRole, // Use the forced role
      status: formData.status || 'active',
      sub: props.subsidiary,
      employerId: formData.email.split('@')[0] || 'user',
      requirePasswordChange: true, // Always require password change for new users
      subId: null // Will be set after finding tenant
    };

    // Find tenant first to ensure we have a valid tenant ID
    try {
      const tenantResponse = await api.get('/tenant', {
        headers: {
          'x-subsidiary': props.subsidiary,
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const tenants = tenantResponse.data.data?.tenants || [];
      const matchingTenant = tenants.find(t => t.sub === props.subsidiary);

      if (matchingTenant) {
        console.log(`Found matching tenant for ${props.subsidiary}:`, matchingTenant);
        // Ensure tenant ID is a valid number
        if (matchingTenant.id) {
          const parsedId = parseInt(matchingTenant.id, 10);
          if (!isNaN(parsedId)) {
            registerData.subId = parsedId;
            console.log(`Using tenant ID: ${registerData.subId} (${typeof registerData.subId})`);
          } else {
            console.warn(`Invalid tenant ID: ${matchingTenant.id}, not using it`);
            registerData.subId = null;
          }
        }
      } else {
        console.warn(`No matching tenant found for subsidiary ${props.subsidiary}`);
      }
    } catch (tenantError) {
      console.error('Error finding tenant:', tenantError);
      // Continue without tenant ID - the backend will try to find it
    }

    // Log the role being sent to ensure it's correct
    console.log('Selected role for new user:', registerData.role);
    console.log('Full register data being sent:', JSON.stringify(registerData, null, 2));

    console.log('Sending user registration data:', registerData);

    // Use the API service to register the user
    const response = await api.post(
      "/auth/register",
      registerData,
      {
        headers: {
          "x-subsidiary": props.subsidiary,
          "Content-Type": "application/json"
        }
      }
    );

    console.log('User registration response:', response);

    if (!response.data) {
      console.error('User registration failed: No data in response');
      throw new Error('Failed to register user');
    }

    const responseData = response.data;
    console.log('User registration successful:', responseData);

    // Force formSubmitting to false immediately
    formSubmitting.value = false;

    // Show success notification immediately
    showNotification('success', 'Success!', `User ${formData.email} created successfully with role ${forcedRole}`);

    // Close the modal immediately
    showUserModal.value = false;

    // Extract the created user from the response
    let newUser = null;

    if (responseData.data && responseData.data.user) {
      newUser = responseData.data.user;
    } else if (responseData.user) {
      newUser = responseData.user;
    }

    if (newUser) {
      console.log('Adding new user to list:', newUser);
      users.value = [...users.value, newUser];
    }

    return responseData;
  } catch (registerError) {
    console.error('Registration approach failed, trying direct user creation:', registerError);

    // Validate the role is present and valid
    if (!formData.role || formData.role === '') {
      console.error('Role is missing or empty, cannot create user!');
      showNotification('error', 'Error', 'Role is required. Please select a valid role.');
      return;
    }

    // Force the role to be a specific value for testing
    const forcedRole = formData.role === 'supervisor' ? 'supervisor' : formData.role;
    console.log('Using forced role for fallback:', forcedRole);

    // Fall back to direct user creation if registration fails
    // Create a new data object for direct user creation
    const createUserData = {
      email: formData.email,
      password: formData.password,
      username: `${formData.firstName};${formData.lastName}`,
      role: forcedRole, // Use the forced role
      status: formData.status || 'active',
      sub: props.subsidiary,
      employerId: formData.email.split('@')[0] || 'user',
      requirePasswordChange: true, // Always require password change for new users
      subId: null // Will be set below
    };

    // Find tenant ID again for the fallback method
    try {
      const tenantResponse = await api.get('/tenant', {
        headers: {
          'x-subsidiary': props.subsidiary,
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const tenants = tenantResponse.data.data?.tenants || [];
      const matchingTenant = tenants.find(t => t.sub === props.subsidiary);

      if (matchingTenant) {
        console.log(`Found matching tenant for fallback: ${props.subsidiary}:`, matchingTenant);
        // Ensure tenant ID is a valid number
        if (matchingTenant.id) {
          const parsedId = parseInt(matchingTenant.id, 10);
          if (!isNaN(parsedId)) {
            createUserData.subId = parsedId;
            console.log(`Using tenant ID for fallback: ${createUserData.subId} (${typeof createUserData.subId})`);
          } else {
            console.warn(`Invalid tenant ID for fallback: ${matchingTenant.id}, not using it`);
            createUserData.subId = null;
          }
        }
      } else {
        console.warn(`No matching tenant found for subsidiary ${props.subsidiary} in fallback`);
      }
    } catch (tenantError) {
      console.error('Error finding tenant for fallback:', tenantError);
      // Continue without tenant ID - the backend will try to find it
    }

    // Log the role again to ensure it's being passed correctly
    console.log('Selected role for fallback user creation:', createUserData.role);
    console.log('Full user creation data being sent:', JSON.stringify(createUserData, null, 2));

    console.log('Sending direct user creation data:', createUserData);

    // Make a direct API call using axios instead of fetch
    const apiResponse = await api.post(
      "/user",
      createUserData,
      {
        headers: {
          "x-subsidiary": props.subsidiary,
          "Content-Type": "application/json",
          "mambuUser": "null" // Set mambuUser to null explicitly
        }
      }
    );

    console.log('Direct user creation response:', apiResponse);

    if (apiResponse && apiResponse.data) {
      const responseData = apiResponse.data;

      // Force formSubmitting to false immediately
      formSubmitting.value = false;

      // Show success notification immediately
      showNotification('success', 'Success!', `User ${formData.email} created successfully with role ${forcedRole}`);

      // Close the modal immediately
      showUserModal.value = false;

      // Extract the created user from the response
      let newUser = null;

      if (responseData.data && responseData.data.user) {
        newUser = responseData.data.user;
      } else if (responseData.user) {
        newUser = responseData.user;
      }

      if (newUser) {
        console.log('Adding new user to list:', newUser);
        users.value = [...users.value, newUser];
      }

      return responseData;
    } else {
      throw new Error('Failed to create user');
    }
  }
};

// Function to handle user update
const handleUserUpdate = async (formData, user) => {
  console.log('Updating user:', user.id);

  try {
    // Create update data object with only the fields we want to update
    const updateData = {
      email: formData.email,
      username: `${formData.firstName};${formData.lastName}`,
      role: formData.role,
      status: formData.status
    };

    // Add password only if provided
    if (formData.password) {
      updateData.password = formData.password;
    }

    console.log('Sending user update data:', updateData);

    // Make a direct API call using axios
    const apiResponse = await api.put(
      `/user/${user.id}`,
      updateData,
      {
        headers: {
          "x-subsidiary": props.subsidiary,
          "Content-Type": "application/json",
          "mambuUser": "null" // Set mambuUser to null explicitly
        }
      }
    );

    console.log('User update API response:', apiResponse);

    if (apiResponse && apiResponse.data) {
      const responseData = apiResponse.data;

      // Extract the updated user from the response
      let updatedUser = null;

      if (responseData.data && responseData.data.user) {
        updatedUser = responseData.data.user;
      } else if (responseData.user) {
        updatedUser = responseData.user;
      } else if (responseData.data) {
        updatedUser = responseData.data;
      }

      if (updatedUser) {
        console.log('Updated user data:', updatedUser);

        // Update the user in the array
        const index = users.value.findIndex(u => u.id === user.id);
        if (index !== -1) {
          // Merge the updated fields with the existing user data
          users.value[index] = {
            ...users.value[index],
            ...updatedUser,
            username: updateData.username,
            email: updateData.email,
            role: updateData.role,
            status: updateData.status
          };

          console.log('Updated user in array:', users.value[index]);
        }
      }

      return responseData;
    } else {
      throw new Error('Invalid response from server');
    }
  } catch (err) {
    console.error('Error updating user:', err);
    throw err;
  }
};

const confirmDelete = (user) => {
  // Check if user has permission to delete users
  if (!canDeleteUsers.value) {
    showNotification('error', 'Access Denied', 'You do not have permission to delete users');
    return;
  }

  userToDelete.value = user;
  showDeleteModal.value = true;
};

const deleteUserConfirmed = async () => {
  if (!userToDelete.value) return;

  deleteSubmitting.value = true;

  try {
    console.log('Deleting user (setting to inactive):', userToDelete.value.id);

    // Try to use the DELETE endpoint first
    try {
      console.log('Attempting to delete user using DELETE endpoint');
      await api.delete(`/user/${userToDelete.value.id}`, {
        headers: {
          'x-subsidiary': props.subsidiary,
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      console.log('User deletion via DELETE endpoint successful');
    } catch (deleteError) {
      console.error('DELETE endpoint failed, falling back to deactivation:', deleteError);

      // Fall back to deactivation if DELETE fails
      try {
        console.log('Attempting to deactivate user using API service');
        await api.put(`/user/${userToDelete.value.id}`,
          { status: 'inactive' },
          {
            headers: {
              'x-subsidiary': props.subsidiary,
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          }
        );

        console.log('User deactivation via API service successful');
      } catch (apiError) {
        console.error('API service deactivation failed, trying direct fetch:', apiError);

        // Fall back to direct fetch if API service fails
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:7080'}/user-access/v1/user/${userToDelete.value.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-subsidiary': props.subsidiary,
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({ status: 'inactive' })
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('User deactivation via fetch failed:', errorData);
          throw new Error(errorData.message || 'Failed to delete user');
        }

        console.log('User deactivation via fetch successful');
      }
    }

    // Remove the user from the local array to simulate deletion
    users.value = users.value.filter(user => user.id !== userToDelete.value.id);

    // Track deleted user IDs to prevent them from reappearing in the UI
    if (!window.deletedUserIds) {
      window.deletedUserIds = new Set();
    }
    window.deletedUserIds.add(userToDelete.value.id);

    // Also track by email as a backup
    if (!window.deletedUserEmails) {
      window.deletedUserEmails = new Set();
    }
    if (userToDelete.value.email) {
      window.deletedUserEmails.add(userToDelete.value.email);
    }

    // Show success notification
    showNotification('success', 'Success!', `User ${userToDelete.value.username || userToDelete.value.email} deleted successfully`);

    // Close the delete modal
    showDeleteModal.value = false;

    // Refresh the user list but filter out any deleted/inactive users
    setTimeout(async () => {
      await fetchUsers();
    }, 1000);
  } catch (err) {
    console.error('Error deleting user:', err);
    error.value = err.message || 'Failed to delete user';

    // Show error notification
    showNotification('error', 'Error!', error.value);

    // Close the delete modal
    showDeleteModal.value = false;
  } finally {
    deleteSubmitting.value = false;
  }
};

const openPasswordModal = (user) => {
  // Check if user has permission to change passwords
  if (!canChangePassword.value) {
    showNotification('error', 'Access Denied', 'You do not have permission to change user passwords');
    return;
  }

  userToChangePassword.value = user;
  passwordForm.value = {
    newPassword: '',
    confirmPassword: ''
  };
  passwordError.value = null;
  showPasswordModal.value = true;
};

const changePassword = async () => {
  if (!userToChangePassword.value) return;

  // Validate passwords match
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    passwordError.value = "Passwords don't match";
    return;
  }

  passwordSubmitting.value = true;

  try {
    // Update user with new password
    await updateUser(
      userToChangePassword.value.id,
      { password: passwordForm.value.newPassword },
      props.subsidiary
    );

    showNotification('success', 'Success!', 'Password changed successfully');
    showPasswordModal.value = false;
  } catch (err) {
    passwordError.value = err.response?.data?.message || 'Failed to change password';
    showNotification('error', 'Error!', passwordError.value);
  } finally {
    passwordSubmitting.value = false;
  }
};

// Close the bulk upload modal and reset state
const closeBulkUploadModal = () => {
  showBulkUploadModal.value = false;
  selectedFile.value = null;
};

// Handle bulk upload completion
const handleBulkUploadComplete = async () => {
  // Close the modal
  closeBulkUploadModal();

  // Show success notification
  showNotification('success', 'Success!', 'Bulk user upload completed');

  // Refresh the user list
  await fetchUsers();
};

// Handle file upload for bulk user import
const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    console.log('File selected:', file.name);
    selectedFile.value = file;
  }
};

// Download CSV template for bulk user upload
const downloadTemplate = () => {
  // Create CSV content with header and example row
  const csvContent = "FirstName,LastName,Email,Password,UserRole,AccountState\n" +
                    "John,Doe,<EMAIL>,password123,supervisor,active\n";

  // Create a blob with the CSV content
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

  // Create a URL for the blob
  const url = URL.createObjectURL(blob);

  // Create a link element
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', 'user_template.csv');

  // Append the link to the document
  document.body.appendChild(link);

  // Click the link to trigger the download
  link.click();

  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  // Show notification
  showNotification('info', 'Template Downloaded', 'CSV template has been downloaded. Valid UserRole values are: systemAdmin, hr, it, supervisor. Valid AccountState values are: active, inactive.');
};

// Upload users from CSV
const uploadUsers = async () => {
  if (!selectedFile.value) {
    showNotification('error', 'Error', 'Please select a file first');
    return;
  }

  try {
    showNotification('info', 'Processing', `Processing file: ${selectedFile.value.name}`);

    // Read the file using FileReader
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        const csvData = e.target.result;
        console.log('Raw CSV data:', csvData);

        // Parse CSV data using Papa Parse
        const parsedData = Papa.parse(csvData, {
          header: true,
          skipEmptyLines: true,
          dynamicTyping: false, // Ensure all values are strings
          transformHeader: (header) => header.trim(), // Trim whitespace from headers
          transform: (value) => {
            // Convert NaN, undefined, or null to empty string to avoid SQL issues
            if (value === 'NaN' || value === 'undefined' || value === 'null' || value === undefined || value === null) {
              return '';
            }
            return value.trim(); // Trim whitespace from values
          }
        });

        console.log('Papa Parse result:', parsedData);

        if (parsedData.errors.length > 0) {
          console.error('CSV parsing errors:', parsedData.errors);
          showNotification('error', 'Error', 'Failed to parse CSV file. Please check the format.');
          return;
        }

        // Ensure parsedData.data is an array
        const csvUsers = Array.isArray(parsedData.data) ? parsedData.data : [];
        console.log('Parsed users:', JSON.stringify(csvUsers, null, 2));

        // Check if we have any users to process
        if (csvUsers.length === 0) {
          console.error('No users found in CSV file');
          showNotification('error', 'Error', 'No users found in CSV file. Please check the format.');
          return;
        }

        // Validate required fields
        const invalidUsers = csvUsers.filter(user =>
          !user || !user.FirstName || !user.LastName || !user.Email ||
          !user.Password || !user.UserRole || !user.AccountState
        );

        if (invalidUsers.length > 0) {
          console.error('Invalid users:', invalidUsers);
          showNotification('error', 'Error', `${invalidUsers.length} users have missing required fields`);
          return;
        }

        // Validate UserRole and AccountState values
        const validRoles = ['systemAdmin', 'hr', 'it', 'supervisor'];
        const validStates = ['active', 'inactive'];

        const usersWithInvalidData = csvUsers.filter(user => {
          if (!user) return true;

          // Check if UserRole is valid
          const hasValidRole = user.UserRole && validRoles.includes(user.UserRole);

          // Check if AccountState is valid
          const hasValidState = user.AccountState && validStates.includes(user.AccountState);

          return !hasValidRole || !hasValidState;
        });

        if (usersWithInvalidData.length > 0) {
          console.error('Users with invalid data:', usersWithInvalidData);
          showNotification('error', 'Error',
            `${usersWithInvalidData.length} users have invalid UserRole or AccountState values`);
          return;
        }

        // Process each user
        const results = [];
        let successCount = 0;
        const createdUsers = [];

        // Process users one by one
        for (const csvUser of csvUsers) {
          try {
            // Create the register data object - same format as single user creation
            const registerData = {
              email: csvUser.Email,
              password: csvUser.Password,
              username: `${csvUser.FirstName};${csvUser.LastName}`,
              role: csvUser.UserRole,
              status: csvUser.AccountState || 'active',
              sub: props.subsidiary,
              employerId: csvUser.Email.split('@')[0] || 'user',
              requirePasswordChange: true,
              // Add subId property if needed later
              subId: null
            };

            console.log(`Creating user ${csvUser.Email} with data:`, JSON.stringify(registerData, null, 2));

            // Find tenant first to ensure we have a valid tenant ID
            try {
              const tenantResponse = await api.get('/tenant', {
                headers: {
                  'x-subsidiary': props.subsidiary,
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
              });

              const tenants = tenantResponse.data.data?.tenants || [];
              const matchingTenant = tenants.find(t => t.sub === props.subsidiary);

              if (matchingTenant) {
                console.log(`Found matching tenant for ${props.subsidiary}:`, matchingTenant);
                // Ensure tenant ID is a valid number
                if (matchingTenant.id) {
                  const parsedId = parseInt(matchingTenant.id, 10);
                  if (!isNaN(parsedId)) {
                    registerData.subId = parsedId;
                    console.log(`Using tenant ID: ${registerData.subId} (${typeof registerData.subId})`);
                  } else {
                    console.warn(`Invalid tenant ID: ${matchingTenant.id}, not using it`);
                    registerData.subId = null;
                  }
                }
              } else {
                console.warn(`No matching tenant found for subsidiary ${props.subsidiary}`);
              }
            } catch (tenantError) {
              console.error('Error finding tenant:', tenantError);
              // Continue without tenant ID - the backend will try to find it
            }

            // Use the API service to register the user - same as handleUserCreation
            const response = await api.post(
              "/auth/register",
              registerData,
              {
                headers: {
                  "x-subsidiary": props.subsidiary,
                  "Content-Type": "application/json"
                }
              }
            );

            console.log(`User ${csvUser.Email} creation response:`, response.data);

            // Extract the created user from the response
            let newUser = null;

            if (response.data) {
              if (response.data.data && response.data.data.user) {
                newUser = response.data.data.user;
              } else if (response.data.user) {
                newUser = response.data.user;
              } else if (response.data.data) {
                newUser = response.data.data;
              } else {
                newUser = response.data;
              }
            }

            if (newUser) {
              console.log('User created successfully:', newUser);
              createdUsers.push(newUser);

              results.push({
                email: csvUser.Email,
                success: true,
                message: 'User created successfully'
              });

              successCount++;
            } else {
              throw new Error('Failed to extract user data from response');
            }
          } catch (error) {
            console.error(`Error creating user ${csvUser.Email}:`, error);
            console.error('Error details:', error.response?.data);

            results.push({
              email: csvUser.Email,
              success: false,
              message: error.response?.data?.message || error.message || 'Failed to create user'
            });
          }
        }

        // Show results notification
        if (successCount > 0) {
          showNotification('success', 'Success',
            `Successfully created ${successCount} out of ${csvUsers.length} users`);

          // Add the created users to the users array
          if (createdUsers.length > 0) {
            users.value = [...users.value, ...createdUsers];
            console.log(`Added ${createdUsers.length} users to the list`);
          }
        } else {
          showNotification('error', 'Error', 'Failed to create any users');
        }

        // Reset the selected file and close the modal
        selectedFile.value = null;
        closeBulkUploadModal();

        // Refresh the user list to ensure we have the latest data
        setTimeout(async () => {
          try {
            await fetchUsers();
            console.log('User list refreshed after bulk upload');
          } catch (refreshError) {
            console.error('Error refreshing user list:', refreshError);
          }
        }, 2000);
      } catch (error) {
        console.error('Error processing CSV data:', error);
        console.error('Error stack:', error.stack);

        // Show a more detailed error message
        let errorMessage = 'Failed to process CSV data';
        if (error.response?.data?.message) {
          errorMessage = `API Error: ${error.response.data.message}`;
        } else if (error.message) {
          errorMessage = `Error: ${error.message}`;
        }

        showNotification('error', 'Error', errorMessage);
      }
    };

    reader.onerror = () => {
      console.error('Error reading file');
      showNotification('error', 'Error', 'Failed to read the file');
    };

    // Read the file as text
    reader.readAsText(selectedFile.value);

  } catch (error) {
    console.error('Error uploading users:', error);
    showNotification('error', 'Error', 'Failed to upload users');
  }
};


const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// Get role badge color
const getRoleBadgeClass = (role) => {
  switch (role) {
    case 'systemAdmin':
    case 'admin':
      return 'bg-blue-100 text-blue-800';
    case 'hr':
      return 'bg-green-100 text-green-800';
    case 'it':
      return 'bg-purple-100 text-purple-800';
    case 'supervisor':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Get status badge color
const getStatusBadgeClass = (status) => {
  return status === 'active'
    ? 'bg-green-100 text-green-800'
    : 'bg-red-100 text-red-800';
};

// Watch for modal visibility changes
watch(showUserModal, (isVisible) => {
  if (isVisible && !editingUser.value) {
    // If the modal is shown and we're not editing a user, reset the form
    console.log('Modal shown for new user - resetting form');

    // Reset form with empty values
    userForm.value = {
      firstName: '',
      lastName: '',
      username: '',
      email: '',
      password: '',
      role: '',
      status: ''
    };

    // Use nextTick to ensure DOM is updated
    nextTick(() => {
      // Directly clear input fields via DOM
      const inputs = document.querySelectorAll('.bg-white input, .bg-white select');
      inputs.forEach(input => {
        if (input.type === 'text' || input.type === 'email' || input.type === 'password') {
          input.value = '';
        } else if (input.tagName === 'SELECT') {
          input.selectedIndex = 0;
        }
      });
    });
  }
});

// Lifecycle hooks
onMounted(() => {
  // Initialize tracking of deleted users
  if (!window.deletedUserIds) {
    window.deletedUserIds = new Set();
  }

  if (!window.deletedUserEmails) {
    window.deletedUserEmails = new Set();
  }

  fetchUsers();
});
</script>
