<template>
  <div class="min-h-screen flex flex-col bg-gradient-to-b from-blue-50 to-gray-100">
    <!-- Force Password Reset Component -->
    <ForcePasswordReset v-if="showForcePasswordReset" @password-changed="handlePasswordChanged" />

    <!-- Header with dynamic colors based on selected subsidiary -->
    <header class="w-full py-6 shadow-md" :style="headerStyle">
      <div class="container mx-auto px-4">
        <h1 class="text-3xl font-bold text-white text-center">
          {{ headerTitle }}
        </h1>
        <p class="text-blue-100 text-center mt-2">
          {{ selectedSubsidiary && subsidiaries[selectedSubsidiary]
             ? subsidiaries[selectedSubsidiary].tagline || 'Secure access management'
             : 'Secure access management for all subsidiaries' }}
        </p>
      </div>
    </header>

    <!-- Login Container -->
    <div class="flex-grow flex items-center justify-center py-12 px-4">
      <div class="bg-white p-10 rounded-xl shadow-2xl w-full max-w-md border border-gray-200"
           :class="{ 'opacity-50 pointer-events-none': showForcePasswordReset }">
        <!-- Logo/Icon -->
        <div class="flex justify-center mb-6">
          <!-- Default logo when no subsidiary is selected -->
          <div v-if="!selectedSubsidiary || !subsidiaries[selectedSubsidiary]"
               class="bg-blue-600 text-white h-20 w-20 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>

          <!-- Subsidiary-specific logo when one is selected -->
          <div v-else
               class="h-24 w-24 rounded-full flex items-center justify-center overflow-hidden border-4"
               :style="{ borderColor: subsidiaries[selectedSubsidiary]?.primaryColor || '#2563eb' }">
            <div v-if="subsidiaries[selectedSubsidiary]?.logo" class="h-full w-full flex items-center justify-center">
              <img :src="subsidiaries[selectedSubsidiary].logo" alt="Subsidiary Logo" class="max-h-full max-w-full object-contain" />
            </div>
            <div v-else class="h-full w-full flex items-center justify-center"
                 :style="{ backgroundColor: subsidiaries[selectedSubsidiary]?.primaryColor || '#2563eb' }">
              <span class="text-white text-2xl font-bold">{{ selectedSubsidiary.substring(0, 2).toUpperCase() }}</span>
            </div>
          </div>
        </div>

        <h2 class="text-2xl font-bold text-center mb-8 text-gray-800">Login to Your Account</h2>

        <!-- Success Alert -->
        <div v-if="successMessage" class="mb-6 p-4 bg-green-50 text-green-700 rounded-lg border border-green-200 flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span>{{ successMessage }}</span>
        </div>

        <!-- Error Alert -->
        <div v-if="error" class="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-200 flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <form @submit.prevent="login" class="space-y-6">
          <!-- Subsidiary Selection -->
          <div>
            <label class="block text-gray-700 font-medium mb-2">Select Subsidiary</label>
            <select
              v-model="selectedSubsidiary"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              required
              :disabled="loading"
            >
              <option value="" disabled>-- Select a subsidiary --</option>
              <option
                v-for="sub in _subsidiaries"
                :key="sub"
                :value="sub"
              >
                {{ sub }}
              </option>
            </select>
            <p class="mt-2 text-sm text-gray-500">You will only be able to access subsidiaries you have permission for.</p>
          </div>

          <!-- Email Input -->
          <div>
            <label class="block text-gray-700 font-medium mb-2">Email</label>
            <input
              type="email"
              v-model="email"
              placeholder="Enter your email"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              required
              :disabled="loading"
            />
          </div>

          <!-- Password Input -->
          <div>
            <label class="block text-gray-700 font-medium mb-2">Password</label>
            <input
              id="password"
              type="password"
              v-model="password"
              placeholder="Enter your password"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              required
              :disabled="loading"
            />
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            class="w-full text-white py-3 px-4 rounded-lg transition-all transform hover:scale-[1.02] font-medium text-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            :style="loginButtonStyle"
            :disabled="loading"
          >
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing in...
            </span>
            <span v-else>Sign In</span>
          </button>
        </form>

        <!-- Footer -->
        <div class="mt-8 text-center text-sm text-gray-500">
          <p>© {{ new Date().getFullYear() }} Digital User Access System</p>
          <p class="mt-1">Secure access management for all subsidiaries</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { subsidiaries } from "@/config/subsidiaries.js";
import { mapActions, mapGetters } from "vuex";
import ForcePasswordReset from "@/views/ForcePasswordReset.vue";

export default {
  name: "UserLogin",
  components: {
    ForcePasswordReset
  },
  data() {
    return {
      subsidiaries,
      selectedSubsidiary: "", // Empty string will match the disabled option
      email: "",
      password: "",
      error: null,
      successMessage: null,
      loading: false,
      showForcePasswordReset: false
    };
  },
  computed: {
    ...mapGetters("auth", ["isAuthenticated", "authError"]),
    _subsidiaries() {
      return Object.keys(subsidiaries);
    },
    // Dynamic header title based on selected subsidiary
    headerTitle() {
      if (this.selectedSubsidiary && this.subsidiaries[this.selectedSubsidiary]) {
        return this.subsidiaries[this.selectedSubsidiary].name || 'Digital User Access System';
      }
      return 'Digital User Access System';
    },

    // Dynamic styling for the header based on selected subsidiary
    headerStyle() {
      if (!this.selectedSubsidiary || !this.subsidiaries[this.selectedSubsidiary]) {
        // Default style when no subsidiary is selected
        return {
          background: 'linear-gradient(to right, #2563eb, #1e40af)'
        };
      }

      // Get the subsidiary's primary color
      const primaryColor = this.subsidiaries[this.selectedSubsidiary].primaryColor || '#2563eb';

      // Create a slightly darker version for the gradient
      const darkerColor = this.adjustColor(primaryColor, -30);

      return {
        background: `linear-gradient(to right, ${primaryColor}, ${darkerColor})`
      };
    },

    // Dynamic styling for the login button based on selected subsidiary
    loginButtonStyle() {
      if (!this.selectedSubsidiary || !this.subsidiaries[this.selectedSubsidiary]) {
        // Default style when no subsidiary is selected
        return {
          background: 'linear-gradient(to right, #2563eb, #1d4ed8)',
          boxShadow: '0 4px 6px rgba(37, 99, 235, 0.25)'
        };
      }

      // Get the subsidiary's primary color
      const primaryColor = this.subsidiaries[this.selectedSubsidiary].primaryColor || '#2563eb';

      // Create a slightly darker version for the gradient
      const darkerColor = this.adjustColor(primaryColor, -20);

      return {
        background: `linear-gradient(to right, ${primaryColor}, ${darkerColor})`,
        boxShadow: `0 4px 6px ${this.hexToRgba(primaryColor, 0.25)}`
      };
    }
  },
  methods: {
    ...mapActions("auth", ["login", "clearError", "clearSession"]),

    // Helper method to adjust a hex color by a percentage
    adjustColor(hex, percent) {
      // Remove the # if present
      hex = hex.replace('#', '');

      // Convert to RGB
      let r = parseInt(hex.substring(0, 2), 16);
      let g = parseInt(hex.substring(2, 4), 16);
      let b = parseInt(hex.substring(4, 6), 16);

      // Adjust each component
      r = Math.max(0, Math.min(255, r + percent));
      g = Math.max(0, Math.min(255, g + percent));
      b = Math.max(0, Math.min(255, b + percent));

      // Convert back to hex
      return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    },

    // Helper method to convert hex to rgba
    hexToRgba(hex, alpha) {
      // Remove the # if present
      hex = hex.replace('#', '');

      // Convert to RGB
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);

      // Return rgba
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    },

    // Handle password changed event from ForcePasswordReset component
    async handlePasswordChanged() {
      console.log('Password changed successfully, logging out...');

      // Log the user out
      await this.$store.dispatch('auth/logout');

      // Reset the form
      this.password = '';
      this.showForcePasswordReset = false;

      // Set success message
      const successMessage = 'Password changed successfully! Please log in with your new password.';

      // Reload the page to ensure a fresh login
      // Check if we're in production (using the base URL path)
      const isProduction = window.location.pathname.includes('/user-access/ui');
      const loginPath = isProduction ? '/user-access/ui/login' : '/login';
      window.location.href = `${loginPath}?fresh=true&message=${encodeURIComponent(successMessage)}`;
    },

    async login() {
      if (!this.selectedSubsidiary) {
        this.error = "Please select a subsidiary.";
        return;
      }

      this.error = null;
      this.loading = true;

      try {
        // Store the selected subsidiary and credentials
        const selectedSub = this.selectedSubsidiary;
        const email = this.email;

        // Check if we're already logged in to a different subsidiary
        const currentUser = this.$store.getters['auth/currentUser'];
        const currentSubsidiary = currentUser?.sub;

        // If we're trying to log in to a different subsidiary than the current one,
        // we need to do a complete page reload to clear everything
        if (currentUser && currentSubsidiary && currentSubsidiary !== selectedSub) {
          console.log(`Detected subsidiary switch from ${currentSubsidiary} to ${selectedSub}`);
          console.log('Storing temporary login info and reloading page');

          // Store the login info temporarily
          sessionStorage.setItem('temp_login_subsidiary', selectedSub);
          sessionStorage.setItem('temp_login_email', email);
          // Don't store the password in plain text

          // Clear all localStorage except theme
          const keys = Object.keys(localStorage);
          for (const key of keys) {
            if (key !== 'theme') {
              localStorage.removeItem(key);
            }
          }

          // Force a complete page reload to clear everything
          window.location.href = '/user-access/ui/login?switch=true';
          return;
        }

        // Check if we're coming from a subsidiary switch reload
        const isSwitching = sessionStorage.getItem('temp_login_subsidiary') === selectedSub;
        if (isSwitching) {
          console.log('Continuing login after page reload for subsidiary switch');
          // Clear the temporary login info
          sessionStorage.removeItem('temp_login_subsidiary');
          sessionStorage.removeItem('temp_login_email');
        }

        console.log(`Login attempt for subsidiary: ${selectedSub}`);

        // ALWAYS clear the session before login to prevent cross-subsidiary issues
        console.log('Clearing session before login attempt');

        // Clear the session completely using the store action
        await this.$store.dispatch('auth/clearSession', { keepSubsidiary: false });

        // Also clear any lingering data directly
        const keys = Object.keys(localStorage);
        for (const key of keys) {
          if (key !== 'theme') {
            localStorage.removeItem(key);
          }
        }

        // Clear cookies
        document.cookie.split(";").forEach(function(c) {
          document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
        });

        console.log('Session completely cleared');

        // Ensure the subsidiary is set correctly after clearing
        this.selectedSubsidiary = selectedSub;

        // Call the login action from the auth store
        const result = await this.$store.dispatch('auth/login', {
          email: this.email,
          password: this.password,
          subsidiary: this.selectedSubsidiary
        });

        if (result.success) {
          // Get the user's actual subsidiary from the response
          const userSubsidiary = result.data?.user?.sub;

          // If the user's subsidiary doesn't match the selected one, show an error
          if (userSubsidiary && userSubsidiary !== this.selectedSubsidiary) {
            // Special case for premierkenya and premiergroup - they are the same entity
            if ((userSubsidiary === 'premierkenya' && this.selectedSubsidiary === 'premiergroup') ||
                (userSubsidiary === 'premiergroup' && this.selectedSubsidiary === 'premierkenya')) {
              console.log('Special case in login: premierkenya and premiergroup are treated as the same entity');

              // For users with premiergroup subsidiary trying to access premierkenya
              if (userSubsidiary === 'premiergroup' && this.selectedSubsidiary === 'premierkenya') {
                console.log('User from premiergroup accessing premierkenya - updating user subsidiary');

                // Update the user object to use premierkenya as the subsidiary
                if (result.data?.user) {
                  result.data.user.sub = 'premierkenya';
                  localStorage.setItem('user', JSON.stringify(result.data.user));
                }
              }

              // Save the selected subsidiary for future use
              localStorage.setItem("selectedSubsidiary", this.selectedSubsidiary);

              // Check if user needs to change password
              if (result.data?.user?.requirePasswordChange) {
                console.log('User needs to change password, showing force password reset');
                this.showForcePasswordReset = true;
                this.loading = false;
                return;
              } else {
                // Redirect to the subsidiary's dashboard
                this.$router.push(`/dashboard/${this.selectedSubsidiary}`);
              }
            } else {
              this.error = `You do not have access to the ${this.selectedSubsidiary} subsidiary. You only have access to ${userSubsidiary}.`;

              // Update the selected subsidiary to match the user's actual subsidiary
              this.selectedSubsidiary = userSubsidiary;
              localStorage.setItem("selectedSubsidiary", userSubsidiary);

              // Wait a moment before redirecting to let the user see the message
              setTimeout(() => {
                this.$router.push(`/dashboard/${userSubsidiary}`);
              }, 2000);
            }
          } else {
            // Save the selected subsidiary for future use
            localStorage.setItem("selectedSubsidiary", this.selectedSubsidiary);

            // Check if user needs to change password
            if (result.data?.user?.requirePasswordChange) {
              console.log('User needs to change password, showing force password reset');
              console.log('User data:', result.data.user);

              // Show the force password reset component
              this.showForcePasswordReset = true;

              // Disable the login form while the password reset is shown
              this.loading = false;

              // Prevent any navigation to dashboard
              return;
            } else {
              // Redirect to the subsidiary's dashboard
              this.$router.push(`/dashboard/${this.selectedSubsidiary}`);
            }
          }
        } else {
          // Handle specific error messages
          if (result.error && result.error.includes("access to")) {
            // Extract the subsidiary the user actually has access to
            const match = result.error.match(/access to the (.*?) subsidiary. You only have access to (.*?)\.$/);
            if (match && match.length >= 3) {
              const requestedSub = match[1];
              const actualSub = match[2];
              this.error = `Subsidiary access denied: You do not have access to ${requestedSub}. You only have access to ${actualSub}.`;

              // Update the selected subsidiary to the one the user has access to
              this.selectedSubsidiary = actualSub;
              localStorage.setItem("selectedSubsidiary", actualSub);
            } else {
              this.error = `You do not have access to the ${this.selectedSubsidiary} subsidiary. Please select a different subsidiary or contact your administrator.`;
            }
          } else {
            this.error = result.error || "Login failed. Please try again.";
          }
        }
      } catch (err) {
        console.error("Login error:", err);
        this.error = err.message || "An unexpected error occurred.";

        // Ensure we're logged out if there was an error
        await this.$store.dispatch('auth/clearSession', { keepSubsidiary: false });
      } finally {
        this.loading = false;
      }
    },
  },
  async created() {
    // Clear any previous auth errors
    this.clearError();

    // Check URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const isFreshLogin = urlParams.get('fresh') === 'true';
    const isSwitchingSubsidiary = urlParams.get('switch') === 'true';

    // Check for success message from password reset
    const message = urlParams.get('message');
    if (message) {
      this.successMessage = decodeURIComponent(message);
      // Clean up the URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }

    // Import config to get the UI path
    const config = require('@/config').default;

    // Get the login paths based on the config and environment
    let loginPaths = ['/', '/login'];

    // Add the production path
    loginPaths.push(`${config.UI_PATH}/login`);

    // Also check for paths with trailing slashes
    loginPaths.push(`${config.UI_PATH}/login/`);

    // Log the paths we're checking for debugging
    console.log('Checking login paths:', loginPaths);

    // If we're coming to the login page directly, after a logout, or switching subsidiaries
    // ensure we're completely logged out
    if (loginPaths.includes(this.$route.path) || isFreshLogin || isSwitchingSubsidiary) {
      console.log('Clearing session on login page load');

      // AGGRESSIVE CLEANUP: Clear everything

      // 1. Clear the session using the store action
      await this.$store.dispatch('auth/clearSession', { keepSubsidiary: false });

      // 2. Clear localStorage (except theme)
      const keys = Object.keys(localStorage);
      for (const key of keys) {
        if (key !== 'theme') {
          localStorage.removeItem(key);
        }
      }

      // 3. Clear cookies
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });

      console.log('Session completely cleared on page load');

      // If we came from a logout or subsidiary switch, clean up the URL
      if (isFreshLogin || isSwitchingSubsidiary) {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }

    // Check if we're coming from a subsidiary switch
    const tempSubsidiary = sessionStorage.getItem('temp_login_subsidiary');
    const tempEmail = sessionStorage.getItem('temp_login_email');

    if (tempSubsidiary && tempEmail) {
      console.log(`Restoring login info after subsidiary switch to ${tempSubsidiary}`);
      this.selectedSubsidiary = tempSubsidiary;
      this.email = tempEmail;
      // Focus the password field
      this.$nextTick(() => {
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
          passwordInput.focus();
        }
      });
    }

    // Load previously selected subsidiary (if any)
    // Only do this if we're not coming from a logout action
    if (!isFreshLogin) {
      const savedSubsidiary = localStorage.getItem("selectedSubsidiary");
      if (savedSubsidiary && this.subsidiaries[savedSubsidiary]) {
        this.selectedSubsidiary = savedSubsidiary;
      }
    }

    // If already authenticated, redirect to dashboard
    // This should only happen if we're navigating within the app
    if (this.isAuthenticated && this.selectedSubsidiary) {
      this.$router.push(`/dashboard/${this.selectedSubsidiary}`);
    }
  },
};
</script>
