<template>
  <div class="min-h-screen flex flex-col items-center justify-center bg-gray-100">
    <!-- Logo -->
    <div class="mb-8">
      <img
        :src="`/images/${subsidiary}-logo.png`"
        alt="Company Logo"
        class="h-20 mx-auto"
        onerror="this.src='/images/platinumkenya-logo.png'; this.onerror=null;"
      />
    </div>

    <!-- Password Reset Card - Styled like Momentum -->
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md border border-gray-200">
      <h1 class="text-xl font-bold text-center mb-6">Password Reset <i class="fas fa-question-circle text-gray-400 ml-2"></i></h1>

      <div v-if="error" class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
        {{ error }}
      </div>

      <form @submit.prevent="resetPassword">
        <!-- Username (Email) -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-medium mb-2" for="username">
            Username
          </label>
          <input
            id="username"
            type="text"
            v-model="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled
          />
        </div>

        <!-- New Password -->
        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-medium mb-2" for="password">
            Password
          </label>
          <input
            id="password"
            :type="showPassword ? 'text' : 'password'"
            v-model="password"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            required
            @input="validatePassword"
          />
          <div v-if="passwordStrength" class="mt-1">
            <div class="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
              <div
                class="h-full rounded-full"
                :class="passwordStrengthClass"
                :style="{ width: `${passwordStrength}%` }"
              ></div>
            </div>
            <p class="text-xs mt-1" :class="passwordStrengthTextClass">
              {{ passwordStrengthText }}
            </p>
          </div>
        </div>

        <!-- Confirm Password -->
        <div class="mb-6">
          <label class="block text-gray-700 text-sm font-medium mb-2" for="confirmPassword">
            Confirm Password
          </label>
          <input
            id="confirmPassword"
            :type="showConfirmPassword ? 'text' : 'password'"
            v-model="confirmPassword"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            required
            @input="validateConfirmPassword"
          />
          <p v-if="confirmPasswordError" class="text-xs text-red-500 mt-1">
            {{ confirmPasswordError }}
          </p>
        </div>

        <!-- Action Buttons - Styled like Momentum -->
        <div class="flex justify-center space-x-4 mt-8">
          <button
            type="button"
            @click="cancel"
            class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            :disabled="loading || !isPasswordValid || confirmPasswordError"
            :class="{ 'opacity-50 cursor-not-allowed': loading || !isPasswordValid || confirmPasswordError }"
          >
            <span v-if="loading">
              <i class="fas fa-spinner fa-spin mr-2"></i>
              Updating...
            </span>
            <span v-else>Change Password</span>
          </button>
        </div>
      </form>

      <div class="mt-8 text-center text-sm text-gray-500">
        Powered by Digital User Access System
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import api from '@/services/apiService';

export default {
  name: 'PasswordResetPage',
  setup() {
    const store = useStore();
    const router = useRouter();

    const email = ref('');
    const password = ref('');
    const confirmPassword = ref('');
    const showPassword = ref(false);
    const showConfirmPassword = ref(false);
    const error = ref('');
    const loading = ref(false);
    const passwordStrength = ref(0);
    const confirmPasswordError = ref('');
    const isPasswordValid = ref(false);
    const subsidiary = ref('');

    // Password strength indicators
    const passwordStrengthText = computed(() => {
      if (passwordStrength.value === 0) return '';
      if (passwordStrength.value < 30) return 'Weak';
      if (passwordStrength.value < 60) return 'Medium';
      if (passwordStrength.value < 80) return 'Strong';
      return 'Very Strong';
    });

    const passwordStrengthClass = computed(() => {
      if (passwordStrength.value < 30) return 'bg-red-500';
      if (passwordStrength.value < 60) return 'bg-yellow-500';
      if (passwordStrength.value < 80) return 'bg-blue-500';
      return 'bg-green-500';
    });

    const passwordStrengthTextClass = computed(() => {
      if (passwordStrength.value < 30) return 'text-red-500';
      if (passwordStrength.value < 60) return 'text-yellow-500';
      if (passwordStrength.value < 80) return 'text-blue-500';
      return 'text-green-500';
    });

    // Validate password as user types
    const validatePassword = () => {
      const pwd = password.value;

      // Reset strength
      let strength = 0;
      isPasswordValid.value = false;

      if (pwd.length === 0) {
        passwordStrength.value = 0;
        return;
      }

      // Length check
      if (pwd.length >= 8) {
        strength += 20;
      } else {
        passwordStrength.value = Math.max(10, pwd.length * 2);
        return;
      }

      // Character type checks
      const hasUppercase = /[A-Z]/.test(pwd);
      const hasLowercase = /[a-z]/.test(pwd);
      const hasNumber = /[0-9]/.test(pwd);
      const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(pwd);

      if (hasUppercase) strength += 20;
      if (hasLowercase) strength += 20;
      if (hasNumber) strength += 20;
      if (hasSpecial) strength += 20;

      // Set password strength
      passwordStrength.value = strength;

      // Check if password meets all requirements
      isPasswordValid.value = (
        pwd.length >= 8 &&
        hasUppercase &&
        hasLowercase &&
        hasNumber &&
        hasSpecial
      );

      // Validate confirm password if it exists
      if (confirmPassword.value) {
        validateConfirmPassword();
      }
    };

    // Validate confirm password
    const validateConfirmPassword = () => {
      if (!confirmPassword.value) {
        confirmPasswordError.value = '';
        return;
      }

      if (password.value !== confirmPassword.value) {
        confirmPasswordError.value = 'Passwords do not match';
      } else {
        confirmPasswordError.value = '';
      }
    };

    // Reset password
    const resetPassword = async () => {
      // Reset error
      error.value = '';

      // Validate passwords match
      if (password.value !== confirmPassword.value) {
        error.value = 'Passwords do not match';
        return;
      }

      // Validate password strength
      if (!isPasswordValid.value) {
        error.value = 'Password does not meet the requirements';
        return;
      }

      try {
        loading.value = true;

        // Get the current user from localStorage
        const user = JSON.parse(localStorage.getItem('user'));

        console.log('Changing password for user:', user);

        // Call API to change password
        // Make sure we're using the user's actual subsidiary, not just a string
        const userSubsidiary = user.sub || subsidiary.value;
        console.log('Using subsidiary for password change:', userSubsidiary);

        await api.post('/auth/change-password', {
          userId: user.id,
          newPassword: password.value
        }, {
          headers: {
            'x-subsidiary': userSubsidiary,
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        // Update the user in localStorage to set requirePasswordChange to false
        if (user) {
          user.requirePasswordChange = false;
          localStorage.setItem('user', JSON.stringify(user));
        }

        // Show success message
        alert('Password changed successfully! Please log in with your new password.');

        // Log the user out
        await store.dispatch('auth/logout');

        // Redirect to login page with a small delay to ensure logout completes
        setTimeout(() => {
          router.push('/login');
        }, 500);
      } catch (err) {
        console.error('Error changing password:', err);
        error.value = err.response?.data?.message || 'Failed to change password';
      } finally {
        loading.value = false;
      }
    };

    // Cancel password reset
    const cancel = async () => {
      // Log the user out
      await store.dispatch('auth/logout');

      // Redirect to login page
      router.push('/login');
    };

    // Initialize component
    onMounted(() => {
      // Get user data from localStorage
      const user = JSON.parse(localStorage.getItem('user'));
      if (user) {
        email.value = user.email;
        // Get subsidiary from user data or localStorage
        subsidiary.value = user.sub || localStorage.getItem('selectedSubsidiary') || 'platinumkenya';
      } else {
        // If no user data, redirect to login
        router.push('/login');
      }
    });

    return {
      email,
      password,
      confirmPassword,
      showPassword,
      showConfirmPassword,
      error,
      loading,
      passwordStrength,
      passwordStrengthText,
      passwordStrengthClass,
      passwordStrengthTextClass,
      confirmPasswordError,
      isPasswordValid,
      subsidiary,
      validatePassword,
      validateConfirmPassword,
      resetPassword,
      cancel
    };
  }
};
</script>
