<template>
  <div
    class="p-8 min-h-screen"
    :style="{ backgroundColor: theme.secondaryColor }"
  >
    <div class=" bg-white p-6 shadow-lg rounded-xl mt-8">
      <h2
        class="text-3xl font-bold mb-6"
        :style="{ color: theme.primaryColor }"
      >
        Audit Log
      </h2>

      <!-- Search and Branch Filters -->
      <div class="flex flex-col sm:flex-row justify-between gap-4 mb-6">
        <div class="w-full sm:w-1/3">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search by user, action, or role..."
            class="border px-4 py-2 w-full rounded-lg focus:outline-none focus:ring-2"
            :style="{
              borderColor: theme.primaryColor,
              boxShadow: `0px 0px 4px ${theme.primaryColor}`,
            }"
          />
        </div>
        <div class="w-full sm:w-1/3">
          <select
            v-model="selectedBranch"
            class="border px-4 py-2 w-full rounded-lg focus:outline-none focus:ring-2"
            :style="{
              borderColor: theme.primaryColor,
              boxShadow: `0px 0px 4px ${theme.primaryColor}`,
            }"
          >
            <option value="">All Branches</option>
            <option
              v-for="branch in uniqueBranches"
              :key="branch"
              :value="branch"
            >
              {{ branch }}
            </option>
          </select>
        </div>
      </div>

      <!-- Download Button -->
      <div class="mb-6 text-right">
        <button
          @click="downloadCSV"
          class="py-2 px-4 rounded-lg text-white"
          :style="{ backgroundColor: theme.primaryColor }"
        >
          Download Audit Log
        </button>
      </div>

      <!-- Audit Table -->
      <div class="overflow-x-auto shadow-lg rounded-lg border border-gray-300">
        <table class="w-full bg-white">
          <thead
            :style="{ backgroundColor: theme.primaryColor, color: 'white' }"
          >
            <tr>
              <th class="p-3 text-left">User</th>
              <th class="p-3 text-left">Role</th>
              <th class="p-3 text-left">Action</th>
              <th class="p-3 text-left">Created By</th>
              <th class="p-3 text-left">Approvers</th>
              <th class="p-3 text-left">Branch</th>
              <th class="p-3 text-left">IP</th>
              <th class="p-3 text-left">Path</th>
              <th class="p-3 text-left">Method</th>
              <th class="p-3 text-left">Agent</th>
              <th class="p-3 text-left">Date Created</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="log in filteredAuditLogs"
              :key="log.id"
              class="border-b text-center hover:bg-gray-50"
            >
              <td class="p-3">{{ log.user || "N/A" }}</td>
              <td class="p-3">{{ log.role || "N/A" }}</td>
              <td class="p-3">
                <span
                  :class="{
                    'text-green-600': log.action.includes('Approved'),
                    'text-red-600': log.action.includes('Rejected'),
                    'text-blue-600': log.action.includes('Created'),
                  }"
                >
                  {{ log.action }}
                </span>
              </td>
              <td class="p-3">{{ log.createdBy || "System" }}</td>
              <td class="p-3">
                <span v-if="Array.isArray(log.approvers)">
                  <div v-for="(approver, index) in log.approvers" :key="index">
                    <span v-if="typeof approver === 'object' && approver.email">
                      <strong>{{ approver.role }}:</strong> {{ approver.email }}
                    </span>
                    <span v-else>{{ approver }}</span>
                  </div>
                </span>
                <span v-else>{{ log.approvers || "N/A" }}</span>
              </td>
              <td class="p-3">{{ log.branch || "N/A" }}</td>
              <td class="p-3">{{ log.ip || "N/A" }}</td>
              <td class="p-3">{{ log.path || "N/A" }}</td>
              <td class="p-3">{{ log.method || "N/A" }}</td>
              <td class="p-3">{{ log.agent || "N/A" }}</td>
              <td class="p-3">{{ formatDate(log.createdAt) }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="mt-4 flex justify-between items-center">
        <button
          class="py-2 px-4 rounded-lg text-white"
          :disabled="currentPage <= 1"
          @click="prevPage"
          :style="{ backgroundColor: theme.primaryColor }"
        >
          Previous
        </button>
        <div class="text-gray-600">
          Page {{ currentPage }} of {{ totalPages }}
        </div>
        <button
          class="py-2 px-4 rounded-lg text-white"
          :disabled="currentPage >= totalPages"
          @click="nextPage"
          :style="{ backgroundColor: theme.primaryColor }"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllAuditLogs } from "@/services/apiService";
import { subsidiaries } from "@/config/subsidiaries";

export default {
  name: "AuditLogPage",
  props: ["subsidiary"],
  computed: {
    theme() {
      return subsidiaries[this.subsidiary] || subsidiaries["platinumKenya"];
    },
    filteredAuditLogs() {
      let filteredLogs = this.auditLogs.filter((log) => {
        const user = log.user ? log.user.toLowerCase() : "";
        const role = log.role ? log.role.toLowerCase() : "";
        const action = log.action ? log.action.toLowerCase() : "";

        const matchesSearch =
          user.includes(this.searchQuery.toLowerCase()) ||
          action.includes(this.searchQuery.toLowerCase()) ||
          role.includes(this.searchQuery.toLowerCase());

        const matchesBranch =
          this.selectedBranch === "" || log.branch === this.selectedBranch;

        return matchesSearch && matchesBranch;
      });

      const startIndex = (this.currentPage - 1) * this.logsPerPage;
      return filteredLogs.slice(startIndex, startIndex + this.logsPerPage);
    },
    uniqueBranches() {
      return [...new Set(this.auditLogs.map((log) => log.branch))];
    },
    totalPages() {
      return Math.ceil(this.filteredAuditLogs.length / this.logsPerPage);
    },
  },
  data() {
    return {
      auditLogs: [],
      searchQuery: "",
      selectedBranch: "",
      currentPage: 1,
      logsPerPage: 10,
    };
  },
  methods: {
    async fetchAuditLogs() {
      try {
        const response = await getAllAuditLogs(this.subsidiary);
        if (response && response.length) {
          this.auditLogs = response.map((log) => ({
            id: log.id,
            user: log.userName || "N/A",
            role: log.role || "N/A",
            action: log.action,
            createdBy: log.createdBy || "System",
            approvers: this.parseApprovers(log.approvers),
            branch: log.branch || "N/A",
            ip: log.ip || "N/A",
            path: log.path || "N/A",
            method: log.method || "N/A",
            agent: log.agent || "N/A",
            createdAt: log.timestamp || new Date(),
          }));
        }
      } catch (error) {
        console.error("Error fetching audit logs:", error);
      }
    },
    parseApprovers(approvers) {
      if (!approvers) return "N/A";
      try {
        if (typeof approvers === "string") {
          const parsedApprovers = JSON.parse(approvers);

          // Process each approver to extract email if present
          return parsedApprovers.map(approver => {
            // Check if the approver string contains an email in parentheses
            if (typeof approver === 'string') {
              const match = approver.match(/^(.*?)\s*\((.*?)\)$/);
              if (match) {
                const role = match[1].trim();
                const email = match[2].trim();
                return { role, email };
              }
            }
            return typeof approver === 'string' ? { role: approver, email: '' } : approver;
          });
        }
        return approvers;
      } catch (error) {
        console.error("Error parsing approvers:", error);
        return [approvers];
      }
    },
    formatDate(date) {
      return new Date(date).toLocaleString();
    },
    nextPage() {
      if (this.currentPage < this.totalPages) this.currentPage++;
    },
    prevPage() {
      if (this.currentPage > 1) this.currentPage--;
    },
    downloadCSV() {
      let csvContent = "data:text/csv;charset=utf-8,";
      csvContent +=
        "User,Role,Action,Created By,Approvers,Branch,IP,Path,Method,Agent,Date Created\n";
      this.filteredAuditLogs.forEach((log) => {
        csvContent +=
          [
            log.user || "N/A",
            log.role || "N/A",
            log.action,
            log.createdBy || "System",
            Array.isArray(log.approvers)
              ? log.approvers.map(a => typeof a === 'object' && a.email
                  ? `${a.role}: ${a.email}`
                  : a).join("; ")
              : log.approvers || "N/A",
            log.branch || "N/A",
            log.ip || "N/A",
            log.path || "N/A",
            log.method || "N/A",
            log.agent || "N/A",
            this.formatDate(log.createdAt),
          ].join(",") + "\n";
      });
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", "audit_logs.csv");
      document.body.appendChild(link);
      link.click();
    },
  },
  mounted() {
    this.fetchAuditLogs();
  },
};
</script>

<style scoped></style>
