<template>
  <div
    class="p-8 min-h-screen"
    :style="{ backgroundColor: theme.secondaryColor}"
  >
    <div class="bg-white p-6 shadow-lg rounded-xl mt-8">
      <h2
        class="text-3xl font-bold mb-6"
        :style="{ color: theme.primaryColor }"
      >
        Reports
      </h2>
      <!-- Filter and Search Section -->
      <div class="flex flex-col sm:flex-row justify-between gap-4 mb-6">
        <div class="w-full sm:w-1/3">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search reports..."
            class="border px-4 py-2 w-full rounded-lg focus:outline-none focus:ring-2"
            :style="{
              borderColor: theme.primaryColor,
              boxShadow: `0px 0px 4px ${theme.primaryColor}`,
            }"
          />
        </div>

        <!-- Date Filter -->
        <div class="w-full sm:w-1/3">
          <input
            v-model="dateRange"
            type="date"
            class="border px-4 py-2 w-full rounded-lg focus:outline-none focus:ring-2"
            :style="{
              borderColor: theme.primaryColor,
              boxShadow: `0px 0px 4px ${theme.primaryColor}`,
            }"
          />
        </div>

        <!-- Hidden Report Type (default to access-history) -->
        <input type="hidden" v-model="reportType" value="access-history" />

        <!-- Export Button -->
        <div>
          <button
            @click="exportReport"
            class="text-white px-6 py-3 rounded-lg shadow-md transition text-lg font-semibold"
            :style="{ backgroundColor: theme.primaryColor }"
          >
            <i class="fas fa-download mr-2"></i> Export Report
          </button>
        </div>
      </div>

      <!-- Report Table -->
      <div
        class="overflow-x-auto shadow-lg rounded-lg border border-gray-300 mb-6"
      >
        <table class="w-full bg-white">
          <thead
            :style="{ backgroundColor: theme.primaryColor, color: 'white' }"
          >
            <tr>
              <th class="p-3 text-left">ID</th>
              <th class="p-3 text-left">System Name</th>
              <th class="p-3 text-left">Branch</th>
              <th class="p-3 text-left">Name</th>
              <th class="p-3 text-left">Role</th>
              <th class="p-3 text-left">Access Type</th>
              <th class="p-3 text-left">Status</th>
              <th class="p-3 text-left">Date</th>
              <th class="p-3 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="report in filteredReports"
              :key="report.id"
              class="border-b hover:bg-gray-50"
            >
              <td class="p-3">{{ report.id }}</td>
              <td class="p-3">{{ report.systemName }}</td>
              <td class="p-3">{{ report.branch }}</td>
              <td class="p-3">{{ report.firstName }} {{ report.lastName }}</td>
              <td class="p-3">{{ report.role }}</td>
              <td class="p-3">
                {{
                  Array.isArray(report.accessType)
                    ? report.accessType.join(", ")
                    : report.accessType
                }}
              </td>
              <td class="p-3">{{ report.approvalStatus }}</td>
              <td class="p-3">{{ formatDate(report.createdAt) }}</td>
              <td class="p-3">
                <button
                  @click="viewReport(report.id)"
                  class="text-blue-600 hover:text-blue-800"
                >
                  <i class="fas fa-eye"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex justify-between items-center">
        <button
          @click="prevPage"
          class="py-2 px-4 rounded-lg text-white"
          :disabled="currentPage <= 1"
          :style="{ backgroundColor: theme.primaryColor }"
        >
          Previous
        </button>
        <div class="text-gray-600">
          Page {{ currentPage }} of {{ totalPages }} (Total:
          {{ totalRecords }} records)
        </div>
        <button
          @click="nextPage"
          class="py-2 px-4 rounded-lg text-white"
          :disabled="currentPage >= totalPages"
          :style="{ backgroundColor: theme.primaryColor }"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { getReports } from "@/services/apiService";
import config from "@/config";
import { subsidiaries } from "@/config/subsidiaries";

export default {
  name: "UserReports",
  props: ["subsidiary"],
  computed: {
    theme() {
      return subsidiaries[this.subsidiary] || subsidiaries["platinumKenya"];
    },
    filteredReports() {
      let filtered = this.reports;

      if (this.searchQuery) {
        filtered = filtered.filter(
          (report) =>
            report.firstName
              ?.toLowerCase()
              .includes(this.searchQuery.toLowerCase()) ||
            report.lastName
              ?.toLowerCase()
              .includes(this.searchQuery.toLowerCase())
        );
      }

      if (this.dateRange) {
        filtered = filtered.filter(
          (report) => report.createdAt >= this.dateRange
        );
      }

      const startIndex = (this.currentPage - 1) * this.reportsPerPage;
      const endIndex = startIndex + this.reportsPerPage;
      return filtered.slice(startIndex, endIndex);
    },
    totalPages() {
      return Math.ceil(this.totalRecords / this.reportsPerPage);
    },
  },
  data() {
    return {
      reports: [],
      searchQuery: "",
      dateRange: "",
      reportType: "access-history", // Always use access-history
      currentPage: 1,
      reportsPerPage: 10,
      totalRecords: 0,
    };
  },

  methods: {
    async fetchReports() {
      const endpoint = this.getEndpointForReportType();

      try {
        const response = await getReports(endpoint, this.subsidiary);
        if (response && response.data) {
          this.reports = response.data;
          this.totalRecords = this.reports.length;
        }
      } catch (error) {
        console.error("Error fetching reports:", error);
        alert(
          "There was an error fetching the report data. Please try again later."
        );
      }
    },
    getEndpointForReportType() {
      switch (this.reportType) {
        case "access-history":
          return "/reports/access-history";
        case "audit-logs":
          return "/reports/audit-logs";
        case "pending-approvals":
          return "/reports/pending-approvals";
        default:
          return "/reports/access-history";
      }
    },
    formatDate(date) {
      const parsedDate = new Date(date);
      return isNaN(parsedDate)
        ? "Invalid Date"
        : parsedDate.toLocaleDateString();
    },
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    },
    async exportReport() {
      try {
        // Show loading indicator or disable button here if needed

        // Always use access-history report type
        const endpoint = `/reports/export/access`;
        const url = `${endpoint}?subId=${this.subsidiary}`;

        // Get the API base URL from config
        const baseURL = config.BACKEND_SERVICE || '';

        // Fetch the file
        const response = await fetch(`${baseURL}${url}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'x-subsidiary': this.subsidiary
          }
        });

        if (!response.ok) {
          throw new Error(`Error: ${response.status} ${response.statusText}`);
        }

        // Get the blob from the response
        const blob = await response.blob();

        // Create a URL for the blob
        const downloadUrl = window.URL.createObjectURL(blob);

        // Create a temporary link element
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `access-report-${this.subsidiary}-${new Date().toISOString().split('T')[0]}.csv`;

        // Append to the document
        document.body.appendChild(link);

        // Trigger the download
        link.click();

        // Clean up
        window.URL.revokeObjectURL(downloadUrl);
        document.body.removeChild(link);
      } catch (error) {
        console.error('Error downloading report:', error);
        alert('Failed to download report. Please try again.');
      }
    },
    viewReport(id) {
      alert(`Viewing report ID: ${id}`);
    },
  },
  mounted() {
    this.fetchReports();
  },
};
</script>

<style scoped>
button {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
