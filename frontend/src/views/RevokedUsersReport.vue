<template>
  <div class="p-6 bg-white shadow-lg rounded-xl mt-20">
    <!-- Header with Export But<PERSON> and Search -->
    <div
      class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6"
    >
      <h2 class="text-2xl font-bold text-gray-800">Revoked Users Report</h2>
      <div class="flex items-center gap-4 w-full sm:w-auto">
        <input
          v-model="search"
          type="text"
          placeholder="Search by name or ID..."
          class="border px-4 py-2 rounded shadow w-full sm:w-64"
        />
        <button
          @click="exportToExcel"
          class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition duration-200 shadow"
        >
          Export
        </button>
      </div>
    </div>

    <div v-if="loading" class="text-gray-500">
      Loading processed revocations...
    </div>

    <!-- Revocations Table -->
    <table
      v-if="!loading"
      class="min-w-full bg-white shadow rounded-lg overflow-hidden text-sm"
    >
      <thead class="bg-gray-100 text-left">
        <tr>
          <th class="p-4">Full Name</th>
          <th class="p-4">Employee ID</th>
          <th class="p-4">Department</th>
          <th class="p-4">Systems</th>
          <th class="p-4">Status</th>
          <th class="p-4">Actioned By</th>
          <th class="p-4">Actioned At</th>
          <th class="p-4">Rejection Reason</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="r in paginatedData"
          :key="r.id"
          class="border-t hover:bg-gray-50"
        >
          <td class="p-4">{{ r.fullName }}</td>
          <td class="p-4">{{ r.employeeId }}</td>
          <td class="p-4">{{ r.department }}</td>
          <td class="p-4">
            <ul class="list-disc ml-4">
              <li v-for="sys in r.systems" :key="sys">{{ sys }}</li>
            </ul>
          </td>
          <td class="p-4">
            <span
              :class="{
                'text-green-600 font-semibold': r.status === 'Approved',
                'text-red-600 font-semibold': r.status === 'Rejected',
              }"
            >
              {{ r.status }}
            </span>
          </td>
          <td class="p-4">{{ r.actionedByIT || "-" }}</td>
          <td class="p-4">{{ formatDate(r.actionedAt) || "-" }}</td>
          <td class="p-4">{{ r.rejectionReason || "-" }}</td>
        </tr>
      </tbody>
    </table>

    <div
      v-if="!loading && paginatedData.length === 0"
      class="text-gray-500 mt-6"
    >
      No records found.
    </div>

    <!-- Pagination Controls -->
    <div
      v-if="filteredData.length > pageSize"
      class="flex justify-between items-center mt-6"
    >
      <p class="text-gray-500 text-sm">
        Showing {{ startIndex + 1 }} -
        {{ Math.min(endIndex, filteredData.length) }} of
        {{ filteredData.length }}
      </p>
      <div class="flex gap-2">
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="px-4 py-2 rounded border bg-white hover:bg-gray-100 disabled:opacity-50"
        >
          Previous
        </button>
        <button
          @click="currentPage++"
          :disabled="endIndex >= filteredData.length"
          class="px-4 py-2 rounded border bg-white hover:bg-gray-100 disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { getCompletedRevocations } from "@/services/apiService";
import * as XLSX from "xlsx";

export default {
  name: "RevokedUsersReport",
  props: ["subsidiary"],
  data() {
    return {
      revocations: [],
      loading: false,
      search: "",
      currentPage: 1,
      pageSize: 10,
    };
  },
  computed: {
    filteredData() {
      return this.revocations.filter((r) => {
        return (
          r.fullName.toLowerCase().includes(this.search.toLowerCase()) ||
          r.employeeId.toLowerCase().includes(this.search.toLowerCase())
        );
      });
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      return this.filteredData.slice(start, start + this.pageSize);
    },
    startIndex() {
      return (this.currentPage - 1) * this.pageSize;
    },
    endIndex() {
      return this.startIndex + this.pageSize;
    },
  },
  async mounted() {
    this.loading = true;
    try {
      const response = await getCompletedRevocations(this.subsidiary);
      this.revocations = response.data || [];
    } catch (error) {
      console.error("Failed to load completed revocations", error);
    } finally {
      this.loading = false;
    }
  },
  methods: {
    formatDate(dateStr) {
      if (!dateStr) return null;
      return new Date(dateStr).toLocaleString();
    },
    exportToExcel() {
      const rows = this.filteredData.map((r) => ({
        "Full Name": r.fullName,
        "Employee ID": r.employeeId,
        Department: r.department,
        Systems: r.systems.join(", "),
        Status: r.status,
        "Actioned By": r.actionedByIT || "-",
        "Actioned At": this.formatDate(r.actionedAt),
        "Rejection Reason": r.rejectionReason || "-",
      }));

      const worksheet = XLSX.utils.json_to_sheet(rows);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Revoked Users");

      XLSX.writeFile(workbook, "revoked_users_report.xlsx");
    },
  },
};
</script>

<style scoped>
th,
td {
  font-size: 0.95rem;
}
</style>
