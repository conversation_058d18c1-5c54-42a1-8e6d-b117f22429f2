<template>
  <div
    class="p-8 min-h-screen"
    :style="{ backgroundColor: theme.secondaryColor}"
  >
    <MainSidebar :subsidiary="subsidiary" />
    <div class="flex-1 p-8  space-y-6">
      <div class="flex justify-between items-center mb-6 pt-4">
        <h1 class="text-3xl font-bold" :style="{ color: theme.primaryColor }">
          {{ theme.name }} Dashboard
        </h1>
        <button
          class="text-white px-6 py-2 rounded-lg shadow-md hover:opacity-90 transition ease-in-out"
          :style="{ backgroundColor: theme.primaryColor }"
        >
          + Add New Request
        </button>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-6">
        <div
          class="bg-white shadow-lg rounded-lg p-6 flex items-center justify-between hover:shadow-xl transition"
        >
          <i class="fas fa-users text-blue-600 text-5xl mr-4"></i>
          <div class="text-right">
            <h2 class="text-lg font-semibold text-gray-700">Total Users</h2>
            <p
              class="text-3xl font-bold"
              :style="{ color: theme.primaryColor }"
            >
              {{ totalUsers }}
            </p>
          </div>
        </div>
        <div
          class="bg-white shadow-lg rounded-lg p-6 flex items-center justify-between hover:shadow-xl transition"
        >
          <i class="fas fa-clock text-yellow-600 text-5xl mr-4"></i>
          <div class="text-right">
            <h2 class="text-lg font-semibold text-gray-700">
              Pending Requests
            </h2>
            <p class="text-3xl font-bold text-yellow-900">
              {{ pendingRequests }}
            </p>
          </div>
        </div>
        <div
          class="bg-white shadow-lg rounded-lg p-6 flex items-center justify-between hover:shadow-xl transition"
        >
          <i class="fas fa-check-circle text-green-600 text-5xl mr-4"></i>
          <div class="text-right">
            <h2 class="text-lg font-semibold text-gray-700">
              Approved Requests
            </h2>
            <p class="text-3xl font-bold text-green-900">
              {{ approvedRequests }}
            </p>
          </div>
        </div>
      </div>

      <!-- Recent Activity and Tracker -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition"
        >
          <h2
            class="text-xl font-semibold mb-4"
            :style="{ color: theme.primaryColor }"
          >
            Recent Activity
          </h2>
          <div v-if="loading" class="text-center text-xl text-gray-600">
            Loading...
          </div>
          <div v-if="error" class="text-center text-xl text-red-600">
            {{ error }}
          </div>
          <ul v-if="!loading && !error" class="space-y-4">
            <li
              v-for="(activity, index) in recentActivities"
              :key="index"
              class="border-b pb-2 flex items-center"
            >
              <i :class="getActivityIcon(activity)" class="mr-2"></i>
              <span>
                {{ activity.firstName }} {{ activity.lastName }}
                <span :class="getActivityClass(activity)">
                  {{ activity.approvalStatus || "created" }}
                </span>
              </span>
              at {{ formatDate(activity.createdAt) }}
            </li>
          </ul>
        </div>

        <div
          class="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition"
        >
          <h2
            class="text-xl font-semibold mb-4"
            :style="{ color: theme.primaryColor }"
          >
            Activity Tracker
          </h2>
          <div v-if="loading" class="text-center text-xl text-gray-600">
            Loading...
          </div>
          <div v-if="error" class="text-center text-xl text-red-600">
            {{ error }}
          </div>
          <div v-if="!loading && !error" class="space-y-6">
            <div class="flex justify-between items-center">
              <span>Processing Requests</span>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-yellow-600 h-2.5 rounded-full"
                  :style="{ width: processingWidth + '%' }"
                ></div>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>Approved Requests</span>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-green-600 h-2.5 rounded-full"
                  :style="{ width: approvedWidth + '%' }"
                ></div>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>Pending Requests</span>
              <div class="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  class="bg-red-600 h-2.5 rounded-full"
                  :style="{ width: pendingWidth + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chart -->
      <div
        class="mt-8 bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition"
      >
        <h2
          class="text-xl font-semibold mb-4"
          :style="{ color: theme.primaryColor }"
        >
          User Activity Overview
        </h2>
        <canvas ref="dashboardChart"></canvas>
      </div>
    </div>
  </div>
</template>

<script>
import MainSidebar from "@/components/MainSidebar.vue";
import { subsidiaries } from "@/config/subsidiaries";
import { nextTick } from "vue";
import {
  getDashboardSummary,
  getRecentActivities,
  getActivityTracker,
} from "@/services/apiService";
import Chart from "chart.js/auto";

export default {
  name: "UserDashboard",
  components: { MainSidebar },
  props: ["subsidiary"],
  computed: {
    theme() {
      return subsidiaries[this.subsidiary] || subsidiaries["platinumKenya"];
    },
  },
  data() {
    return {
      totalUsers: 0,
      pendingRequests: 0,
      approvedRequests: 0,
      loading: true,
      error: null,
      chartInstance: null,
      recentActivities: [],
      processingWidth: 0,
      approvedWidth: 0,
      pendingWidth: 0,
    };
  },
  methods: {
    async fetchDashboardData() {
      this.loading = true;
      this.error = null;
      try {
        const data = await getDashboardSummary(this.subsidiary);
        if (data) {
          this.totalUsers = data.totalUsers;
          this.pendingRequests = data.pendingRequests;
          this.approvedRequests = data.approvedRequests || 0;
        }
      } catch (error) {
        this.error = "Error fetching dashboard data.";
        console.error("Error fetching dashboard data:", error);
      } finally {
        this.loading = false;
        nextTick(() => {
          this.initChart();
        });
      }
    },

    async fetchRecentActivities() {
      try {
        const data = await getRecentActivities(this.subsidiary);
        this.recentActivities = [...data.accessRequests, ...data.approvals];
      } catch (error) {
        this.error = "Error fetching recent activities.";
        console.error("Error fetching recent activities:", error);
      }
    },

    async fetchActivityTracker() {
      try {
        const data = await getActivityTracker(this.subsidiary);
        this.processingWidth =
          (data.processingRequests / data.totalRequests) * 100;
        this.approvedWidth = (data.approvedRequests / data.totalRequests) * 100;
        this.pendingWidth = (data.pendingRequests / data.totalRequests) * 100;
      } catch (error) {
        this.error = "Error fetching activity tracker data.";
        console.error("Error fetching activity tracker data:", error);
      }
    },

    formatDate(date) {
      return new Date(date).toLocaleString();
    },

    initChart() {
      const canvas = this.$refs.dashboardChart;
      if (!canvas) return;

      if (this.chartInstance) {
        this.chartInstance.destroy();
      }

      this.chartInstance = new Chart(canvas, {
        type: "bar",
        data: {
          labels: ["Users", "Pending Requests", "Approved Requests"],
          datasets: [
            {
              label: "User Metrics",
              data: [
                this.totalUsers,
                this.pendingRequests,
                this.approvedRequests,
              ],
              backgroundColor: ["#1E40AF", "#D97706", "#15803D"],
            },
          ],
        },
      });
    },

    getActivityIcon(activity) {
      return activity.approvalStatus === "Approved"
        ? "fas fa-check-circle text-green-500"
        : activity.approvalStatus === "Rejected"
        ? "fas fa-times-circle text-red-500"
        : "fas fa-user-check text-blue-500";
    },

    getActivityClass(activity) {
      return activity.approvalStatus === "Approved"
        ? "text-green-600 font-semibold"
        : activity.approvalStatus === "Rejected"
        ? "text-red-600 font-semibold"
        : "text-blue-600 font-semibold";
    },
  },
  mounted() {
    // Get the current user's subsidiary from the store
    const currentUser = this.$store.getters['auth/currentUser'];
    const userSubsidiary = currentUser?.sub;

    console.log('Dashboard mounted - User subsidiary:', userSubsidiary);
    console.log('Dashboard mounted - Route subsidiary:', this.subsidiary);

    // If the user's subsidiary doesn't match the route subsidiary, redirect
    if (userSubsidiary && this.subsidiary !== userSubsidiary) {
      console.warn(`Subsidiary mismatch: User belongs to ${userSubsidiary}, tried to access ${this.subsidiary}`);
      console.warn('Redirecting to correct subsidiary dashboard');

      // Redirect to the correct subsidiary dashboard
      this.$router.replace(`/dashboard/${userSubsidiary}`);
      return;
    }

    // Fetch data only if we're on the correct subsidiary
    this.fetchDashboardData();
    this.fetchRecentActivities();
    this.fetchActivityTracker();
  },
};
</script>

<style scoped>
@import "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css";
</style>
