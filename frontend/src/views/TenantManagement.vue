<template>
  <div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="mb-2">
        <h1 class="text-xl font-bold text-orange-600">
          Tenant Management
        </h1>
        <p class="text-gray-500 text-sm font-medium">Manage system tenants and their configurations</p>
      </div>

      <div class="flex mb-6">
        <button
          @click="fetchTenants"
          class="flex items-center justify-center bg-blue-500 text-white px-4 py-2 rounded-md mr-2"
        >
          <i class="fas fa-sync-alt mr-2"></i> Refresh
        </button>

        <button
          @click="createNewTenant"
          class="flex items-center justify-center bg-green-500 text-white px-4 py-2 rounded-md mr-2"
        >
          <i class="fas fa-plus mr-2"></i> Create Tenant
        </button>

        <button
          @click="showConfigTenantModal = true"
          class="flex items-center justify-center bg-purple-500 text-white px-4 py-2 rounded-md"
        >
          <i class="fas fa-cog mr-2"></i> Create from Config
        </button>
      </div>

      <!-- Loading and Error States -->
      <div v-if="loading" class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>

      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p>{{ error }}</p>
        <button @click="fetchTenants" class="text-blue-500 underline mt-2">Try Again</button>
      </div>

      <!-- Tenant Table -->
      <div v-else-if="tenants.length === 0" class="text-center py-8">
        <p class="text-gray-500">No tenants found. Create a new tenant to get started.</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subsidiary</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Matcher</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="tenant in paginatedTenants" :key="tenant.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">{{ tenant.name }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ tenant.sub }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ tenant.url }}</td>
              <td class="px-6 py-4 whitespace-nowrap">{{ tenant.matcher || '-' }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-6">
                  <button
                    @click="editTenant(tenant)"
                    class="text-blue-600"
                    title="Edit Tenant"
                  >
                    <span class="flex items-center">
                      <i class="fas fa-edit"></i>
                    </span>
                  </button>
                  <button
                    @click="confirmDeleteTenant(tenant)"
                    class="text-red-600"
                    title="Delete Tenant"
                  >
                    <span class="flex items-center">
                      <i class="fas fa-trash"></i>
                    </span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="tenants.length > itemsPerPage" class="flex justify-center mt-6">
        <nav class="flex items-center">
          <button
            @click="currentPage > 1 ? currentPage-- : null"
            :disabled="currentPage === 1"
            class="px-3 py-1 rounded-md mr-2"
            :class="currentPage === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-blue-500 text-white'"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          <div class="flex space-x-1">
            <button
              v-for="page in totalPages"
              :key="page"
              @click="currentPage = page"
              class="px-3 py-1 rounded-md"
              :class="currentPage === page ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'"
            >
              {{ page }}
            </button>
          </div>
          <button
            @click="currentPage < totalPages ? currentPage++ : null"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 rounded-md ml-2"
            :class="currentPage === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-blue-500 text-white'"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </nav>
      </div>
    </div>

    <!-- Tenant Modal -->
    <div v-if="showTenantModal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="fixed inset-0 bg-transparent" @click="closeTenantModal"></div>
      <div class="bg-white rounded shadow-md p-6 w-full max-w-2xl relative z-10">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-base font-medium text-gray-800">
            {{ editingTenant ? 'Edit Tenant' : 'Create New Tenant' }}
          </h2>
          <button @click="closeTenantModal" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <form @submit.prevent="saveTenant">
          <!-- Form Error -->
          <div v-if="formError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ formError }}
          </div>

          <!-- Name -->
          <div class="mb-4">
            <label class="block text-sm mb-2 text-gray-700 font-medium" for="name">
              Tenant Name
            </label>
            <input
              id="name"
              v-model="tenantForm.name"
              type="text"
              class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
              required
              placeholder="e.g. Platinum Kenya Credit IT Department"
            />
          </div>

          <!-- Subsidiary Code -->
          <div class="mb-4">
            <label class="block text-sm mb-2 text-gray-700 font-medium" for="sub">
              Subsidiary Code
            </label>
            <input
              id="sub"
              v-model="tenantForm.sub"
              type="text"
              class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
              required
              placeholder="e.g. platinumkenya"
              :disabled="editingTenant"
            />
            <p v-if="editingTenant" class="text-xs text-gray-500 mt-1">
              Subsidiary code cannot be changed after creation
            </p>
          </div>

          <!-- URL -->
          <div class="mb-4">
            <label class="block text-sm mb-2 text-gray-700 font-medium" for="url">
              URL
            </label>
            <input
              id="url"
              v-model="tenantForm.url"
              type="url"
              class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
              required
              placeholder="e.g. https://platinumcredit.co.ke"
            />
          </div>

          <!-- Matcher -->
          <div class="mb-4">
            <label class="block text-sm mb-2 text-gray-700 font-medium" for="matcher">
              Domain Matcher
            </label>
            <input
              id="matcher"
              v-model="tenantForm.matcher"
              type="text"
              class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
              placeholder="e.g. platinumcredit.co.ke"
            />
            <p class="text-xs text-gray-500 mt-1">
              Used to match email domains to this tenant (optional)
            </p>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-center w-full pt-6">
            <button
              type="submit"
              class="w-1/2 py-3 rounded bg-black text-white flex items-center justify-center text-base font-medium"
              :disabled="formSubmitting"
            >
              <span v-if="formSubmitting">
                <i class="fas fa-spinner fa-spin mr-2"></i> Saving...
              </span>
              <span v-else class="flex items-center justify-center">
                <i class="fas fa-building mr-2"></i>
                {{ editingTenant ? 'Update Tenant' : 'Create Tenant' }}
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="fixed inset-0 bg-transparent" @click="closeDeleteModal"></div>
      <div class="bg-white rounded shadow-md p-6 w-full max-w-md relative z-10">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-base font-medium text-gray-800">Confirm Delete</h2>
          <button @click="closeDeleteModal" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="mb-6">
          <p class="text-gray-700">
            Are you sure you want to delete the tenant <strong>{{ tenantToDelete?.name }}</strong>?
          </p>
          <p class="text-red-600 text-sm mt-2">
            This action cannot be undone. All associated data will be permanently deleted.
          </p>
        </div>

        <div class="flex justify-end space-x-4">
          <button
            @click="closeDeleteModal"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
          >
            Cancel
          </button>
          <button
            @click="deleteTenantConfirmed"
            class="px-4 py-2 bg-red-600 text-white rounded-md"
            :disabled="deleteSubmitting"
          >
            <span v-if="deleteSubmitting">
              <i class="fas fa-spinner fa-spin mr-2"></i> Deleting...
            </span>
            <span v-else>Delete Tenant</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Create from Config Modal -->
    <div v-if="showConfigTenantModal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="fixed inset-0 bg-transparent" @click="showConfigTenantModal = false"></div>
      <div class="bg-white rounded shadow-md p-6 w-full max-w-2xl relative z-10">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-base font-medium text-gray-800">
            Create Tenant from Configuration
          </h2>
          <button @click="showConfigTenantModal = false" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="mb-4">
          <p class="text-gray-600 mb-2">
            Select a subsidiary from the configuration to create a new tenant. This will use the predefined settings.
          </p>
        </div>

        <!-- Config Error -->
        <div v-if="configError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {{ configError }}
        </div>

        <!-- Subsidiary Selection -->
        <div class="mb-6">
          <label class="block text-sm mb-2 text-gray-700 font-medium" for="configSubsidiary">
            Select Subsidiary
          </label>
          <select
            id="configSubsidiary"
            v-model="selectedConfigSubsidiary"
            class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
            required
          >
            <option value="" disabled>Select a subsidiary</option>
            <option
              v-for="(config, key) in subsidiaryConfigs"
              :key="key"
              :value="key"
              :disabled="existingSubsidiaries.includes(key)"
            >
              {{ config.name }} {{ existingSubsidiaries.includes(key) ? '(Already exists)' : '' }}
            </option>
          </select>
        </div>

        <!-- Preview -->
        <div v-if="selectedConfigSubsidiary" class="mb-6 p-4 bg-gray-50 rounded-md">
          <h3 class="text-sm font-medium text-gray-700 mb-2">Preview:</h3>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <p class="text-xs text-gray-500">Name:</p>
              <p class="text-sm">{{ subsidiaryConfigs[selectedConfigSubsidiary].name }}</p>
            </div>
            <div>
              <p class="text-xs text-gray-500">Subsidiary Code:</p>
              <p class="text-sm">{{ selectedConfigSubsidiary }}</p>
            </div>
            <div>
              <p class="text-xs text-gray-500">Colors:</p>
              <div class="flex items-center space-x-2">
                <div
                  class="w-6 h-6 rounded-full"
                  :style="{ backgroundColor: subsidiaryConfigs[selectedConfigSubsidiary].primaryColor }"
                ></div>
                <div
                  class="w-6 h-6 rounded-full"
                  :style="{ backgroundColor: subsidiaryConfigs[selectedConfigSubsidiary].secondaryColor }"
                ></div>
              </div>
            </div>
            <div>
              <p class="text-xs text-gray-500">Logo:</p>
              <img
                :src="subsidiaryConfigs[selectedConfigSubsidiary].logo"
                alt="Logo"
                class="h-8 object-contain"
              />
            </div>
          </div>
        </div>

        <!-- URL Input -->
        <div class="mb-4">
          <label class="block text-sm mb-2 text-gray-700 font-medium" for="configUrl">
            URL
          </label>
          <input
            id="configUrl"
            v-model="configUrl"
            type="url"
            class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
            required
            placeholder="e.g. https://platinumcredit.co.ke"
          />
        </div>

        <!-- Matcher Input -->
        <div class="mb-4">
          <label class="block text-sm mb-2 text-gray-700 font-medium" for="configMatcher">
            Domain Matcher
          </label>
          <input
            id="configMatcher"
            v-model="configMatcher"
            type="text"
            class="w-full rounded border border-gray-300 px-4 py-2.5 text-sm bg-blue-50"
            placeholder="e.g. platinumcredit.co.ke"
          />
          <p class="text-xs text-gray-500 mt-1">
            Used to match email domains to this tenant (optional)
          </p>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-center w-full pt-6">
          <button
            @click="createTenantFromConfig"
            class="w-1/2 py-3 rounded bg-purple-600 text-white flex items-center justify-center text-base font-medium"
            :disabled="configSubmitting || !selectedConfigSubsidiary || !configUrl"
          >
            <span v-if="configSubmitting">
              <i class="fas fa-spinner fa-spin mr-2"></i> Creating...
            </span>
            <span v-else class="flex items-center justify-center">
              <i class="fas fa-cog mr-2"></i>
              Create Tenant from Config
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Notification Toast -->
    <div
      v-if="notification.show"
      class="fixed bottom-4 right-4 p-4 rounded-md shadow-lg z-50"
      :class="{
        'bg-green-100 border-l-4 border-green-500': notification.type === 'success',
        'bg-red-100 border-l-4 border-red-500': notification.type === 'error',
        'bg-blue-100 border-l-4 border-blue-500': notification.type === 'info'
      }"
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <i
            class="fas mr-2"
            :class="{
              'fa-check-circle text-green-500': notification.type === 'success',
              'fa-exclamation-circle text-red-500': notification.type === 'error',
              'fa-info-circle text-blue-500': notification.type === 'info'
            }"
          ></i>
        </div>
        <div>
          <p class="font-medium" :class="{
            'text-green-800': notification.type === 'success',
            'text-red-800': notification.type === 'error',
            'text-blue-800': notification.type === 'info'
          }">
            {{ notification.title }}
          </p>
          <p class="text-sm" :class="{
            'text-green-700': notification.type === 'success',
            'text-red-700': notification.type === 'error',
            'text-blue-700': notification.type === 'info'
          }">
            {{ notification.message }}
          </p>
        </div>
        <div class="ml-auto pl-3">
          <button
            @click="closeNotification"
            class="inline-flex text-gray-400 hover:text-gray-500"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import api, { getAllTenants, createTenant, updateTenant, deleteTenant as apiDeleteTenant } from '@/services/apiService';
import { canAccess } from '@/utils/accessControl';
import { subsidiaries as subsidiaryConfigs } from '@/config/subsidiaries';

// Store and router
const store = useStore();
const router = useRouter();

// Check if user has permission to access this page
const currentUser = store.getters['auth/currentUser'];
const hasPermission = canAccess('tenants', currentUser);

if (!hasPermission) {
  console.warn(`User ${currentUser?.username} with role ${currentUser?.role} does not have permission to access Tenant Management`);
  // Redirect to dashboard if user doesn't have permission
  router.push(`/dashboard/${store.getters['auth/currentSubsidiary'] || 'platinumkenya'}`);
}

// State
const tenants = ref([]);
const loading = ref(true);
const error = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(10);
const searchQuery = ref('');

// Tenant modal state
const showTenantModal = ref(false);
const editingTenant = ref(null);
const tenantForm = ref({
  name: '',
  sub: '',
  url: '',
  matcher: ''
});
const formError = ref(null);
const formSubmitting = ref(false);

// Delete modal state
const showDeleteModal = ref(false);
const tenantToDelete = ref(null);
const deleteSubmitting = ref(false);

// Config tenant modal state
const showConfigTenantModal = ref(false);
const selectedConfigSubsidiary = ref('');
const configUrl = ref('');
const configMatcher = ref('');
const configError = ref(null);
const configSubmitting = ref(false);
const existingSubsidiaries = ref([]);

// Notification state
const notification = ref({
  show: false,
  type: 'info', // 'success', 'error', 'info'
  title: '',
  message: '',
  timeout: null
});

// Computed properties
const filteredTenants = computed(() => {
  if (!searchQuery.value) return tenants.value;

  const query = searchQuery.value.toLowerCase();
  return tenants.value.filter(tenant =>
    tenant.name.toLowerCase().includes(query) ||
    tenant.sub.toLowerCase().includes(query) ||
    tenant.url.toLowerCase().includes(query) ||
    (tenant.matcher && tenant.matcher.toLowerCase().includes(query))
  );
});

const paginatedTenants = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredTenants.value.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(filteredTenants.value.length / itemsPerPage.value) || 1;
});

// Methods
const fetchTenants = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await getAllTenants();
    tenants.value = response.data || [];
    console.log('Tenants loaded:', tenants.value);

    // Update the list of existing subsidiaries
    updateExistingSubsidiaries();
  } catch (err) {
    console.error('Error fetching tenants:', err);
    error.value = err.message || 'Failed to load tenants';
  } finally {
    loading.value = false;
  }
};

const createNewTenant = () => {
  editingTenant.value = null;
  tenantForm.value = {
    name: '',
    sub: '',
    url: '',
    matcher: ''
  };
  formError.value = null;
  showTenantModal.value = true;
};

const editTenant = (tenant) => {
  editingTenant.value = tenant;
  tenantForm.value = {
    name: tenant.name,
    sub: tenant.sub,
    url: tenant.url,
    matcher: tenant.matcher || ''
  };
  formError.value = null;
  showTenantModal.value = true;
};

const closeTenantModal = () => {
  showTenantModal.value = false;
  formError.value = null;
  editingTenant.value = null;
};

const saveTenant = async () => {
  formSubmitting.value = true;
  formError.value = null;

  try {
    if (editingTenant.value) {
      // Update existing tenant
      await updateTenant(editingTenant.value.id, tenantForm.value);
      showNotification('success', 'Tenant Updated', `Tenant ${tenantForm.value.name} has been updated successfully.`);
    } else {
      // Create new tenant
      await createTenant(tenantForm.value);
      showNotification('success', 'Tenant Created', `Tenant ${tenantForm.value.name} has been created successfully.`);
    }

    // Close modal and refresh tenant list
    closeTenantModal();
    fetchTenants();
  } catch (err) {
    console.error('Error saving tenant:', err);
    formError.value = err.response?.data?.message || err.message || 'Failed to save tenant';
  } finally {
    formSubmitting.value = false;
  }
};

const confirmDeleteTenant = (tenant) => {
  tenantToDelete.value = tenant;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  tenantToDelete.value = null;
};

const deleteTenantConfirmed = async () => {
  if (!tenantToDelete.value) return;

  deleteSubmitting.value = true;

  try {
    await apiDeleteTenant(tenantToDelete.value.id);
    showNotification('success', 'Tenant Deleted', `Tenant ${tenantToDelete.value.name} has been deleted successfully.`);
    closeDeleteModal();
    fetchTenants();
  } catch (err) {
    console.error('Error deleting tenant:', err);
    showNotification('error', 'Delete Failed', err.response?.data?.message || err.message || 'Failed to delete tenant');
  } finally {
    deleteSubmitting.value = false;
  }
};

const showNotification = (type, title, message) => {
  // Clear any existing timeout
  if (notification.value.timeout) {
    clearTimeout(notification.value.timeout);
  }

  // Set notification
  notification.value = {
    show: true,
    type,
    title,
    message,
    timeout: setTimeout(() => {
      notification.value.show = false;
    }, 5000) // Auto-hide after 5 seconds
  };
};

const closeNotification = () => {
  if (notification.value.timeout) {
    clearTimeout(notification.value.timeout);
  }
  notification.value.show = false;
};

// Create tenant from config
const createTenantFromConfig = async () => {
  if (!selectedConfigSubsidiary.value || !configUrl.value) {
    configError.value = 'Please select a subsidiary and provide a URL';
    return;
  }

  configSubmitting.value = true;
  configError.value = null;

  try {
    const config = subsidiaryConfigs[selectedConfigSubsidiary.value];

    // Create tenant data from config
    const tenantData = {
      name: config.name,
      sub: selectedConfigSubsidiary.value,
      url: configUrl.value,
      matcher: configMatcher.value || selectedConfigSubsidiary.value
    };

    // Create the tenant
    await createTenant(tenantData);

    // Show success notification
    showNotification(
      'success',
      'Tenant Created',
      `Tenant ${config.name} has been created successfully from configuration.`
    );

    // Close modal and refresh tenant list
    showConfigTenantModal.value = false;
    selectedConfigSubsidiary.value = '';
    configUrl.value = '';
    configMatcher.value = '';

    // Refresh tenant list
    fetchTenants();
  } catch (err) {
    console.error('Error creating tenant from config:', err);
    configError.value = err.response?.data?.message || err.message || 'Failed to create tenant from configuration';
  } finally {
    configSubmitting.value = false;
  }
};

// Update existing subsidiaries list
const updateExistingSubsidiaries = () => {
  existingSubsidiaries.value = tenants.value.map(tenant => tenant.sub);
};

// Watch for changes in tenants to update existing subsidiaries list
watch(tenants, () => {
  updateExistingSubsidiaries();
}, { deep: true });

// Fetch tenants on component mount
onMounted(() => {
  fetchTenants();
});
</script>
