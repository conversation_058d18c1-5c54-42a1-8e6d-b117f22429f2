<script setup lang="js">
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import { createUser } from "../services/apiService";

const route = useRoute();
const router = useRouter();
const isLoading = ref(true);
const authenticated = ref(false);

onMounted(() => {
  authenticateUser(route.query);
});

const authenticateUser = (query) => {
  console.log(query, "query");
  isLoading.value = true;
  createUser(query)
    .then((response) => {
      const data = response.data;
      isLoading.value = false;
      authenticated.value = data.data.authenticated;
      if (data.data.authenticated) {
        setTimeout(() => {
          router.push(`/dashboard/${data.data.tenant.sub}`);
        }, 2500);
      }
    })
    .catch(() => {
      isLoading.value = false;
      authenticated.value = false;
      router.push("/login");
    });
};
</script>

<template>
  <div
    class="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-100 to-slate-200 p-6"
  >
    <!-- Header -->
    <div class="mb-10 text-center">
      <h1
        class="text-3xl md:text-4xl font-extrabold text-indigo-700 drop-shadow"
      >
        Digital User Access System
      </h1>
      <p class="mt-2 text-gray-600 text-sm md:text-base">
        Sign in securely to continue.
      </p>
    </div>

    <!-- Auth Card -->
    <div
      class="bg-white/30 border border-white/20 backdrop-blur-xl shadow-2xl rounded-3xl px-10 py-12 text-center w-full max-w-lg animate-fade-in"
    >
      <!-- Big loader or icon -->
      <div class="flex justify-center mb-8">
        <div
          class="w-20 h-20 rounded-full border-8 border-indigo-300 border-t-indigo-600 animate-spin shadow-xl"
        ></div>
      </div>

      <h2 class="text-2xl font-semibold text-gray-800 mb-2">
        Authenticating...
      </h2>
      <p class="text-gray-600 mb-6">
        {{
          isLoading
            ? "Please wait while we verify your login credentials securely..."
            : authenticated
            ? "Success! Redirecting you to your dashboard."
            : "Authentication failed. Please try again."
        }}
      </p>

      <button
        :disabled="isLoading"
        @click="authenticateUser(route.query)"
        class="w-full py-3 text-lg font-semibold rounded-xl transition-all duration-300 focus:outline-none bg-indigo-500 text-white hover:bg-indigo-600 hover:scale-105 disabled:opacity-50"
      >
        {{
          isLoading && !authenticated
            ? "Verifying..."
            : authenticated
            ? "Success ✔"
            : "Try Again ✖"
        }}
      </button>

      <!-- Security Note -->
      <div class="mt-6 text-xs text-gray-500 italic">
        🔐 Your login is protected and encrypted.
      </div>
    </div>

    <!-- Support Footer -->
    <div class="mt-8 text-sm text-gray-500 text-center">
      Need help?
      <a href="mailto:<EMAIL>" class="text-indigo-600 underline"
        >Contact IT Support</a
      >
    </div>
  </div>
</template>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}
</style>
