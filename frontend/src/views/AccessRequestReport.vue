<template>
  <div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-orange-600">User Access Request Report</h1>
        <p class="text-gray-500 text-sm">Generate and download reports of user access requests</p>
      </div>

      <!-- Filter Section -->
      <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold mb-4">Filter Options</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Date Range -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <input
              type="date"
              v-model="filters.startDate"
              class="w-full rounded border border-gray-300 px-3 py-2 text-sm"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
            <input
              type="date"
              v-model="filters.endDate"
              class="w-full rounded border border-gray-300 px-3 py-2 text-sm"
            />
          </div>

          <!-- Status -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              v-model="filters.status"
              class="w-full rounded border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">All Statuses</option>
              <option value="Pending">Pending</option>
              <option value="Approved by HR">Approved by HR</option>
              <option value="Approved by IT">Approved by IT</option>
              <option value="Rejected">Rejected</option>
            </select>
          </div>

          <!-- System Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">System Name</label>
            <select
              v-model="filters.systemName"
              class="w-full rounded border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">All Systems</option>
              <option v-for="system in uniqueSystems" :key="system" :value="system">
                {{ system }}
              </option>
            </select>
          </div>

          <!-- Department -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
            <select
              v-model="filters.department"
              class="w-full rounded border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">All Departments</option>
              <option v-for="dept in uniqueDepartments" :key="dept" :value="dept">
                {{ dept }}
              </option>
            </select>
          </div>

          <!-- Role -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <select
              v-model="filters.role"
              class="w-full rounded border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">All Roles</option>
              <option v-for="role in uniqueRoles" :key="role" :value="role">
                {{ role }}
              </option>
            </select>
          </div>
        </div>

        <!-- Filter Buttons -->
        <div class="flex justify-end mt-4 space-x-3">
          <button
            @click="resetFilters"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Reset Filters
          </button>
          <button
            @click="fetchReport"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Apply Filters
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-10">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline"> {{ error }}</span>
      </div>

      <!-- Results Section -->
      <div v-else-if="reportData.length > 0">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Results ({{ reportData.length }} requests)</h2>
          <div class="flex space-x-2">
            <button
              @click="downloadCSV"
              class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
            >
              <i class="fas fa-file-csv mr-2"></i> Download CSV
            </button>
            <button
              @click="downloadExcel"
              class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
            >
              <i class="fas fa-file-excel mr-2"></i> Download Excel
            </button>
          </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ID
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  System
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="request in paginatedData" :key="request.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">{{ request.id }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ request.firstName }} {{ request.lastName }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ request.email }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ request.systemName }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ request.department }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ request.role }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 py-1 text-xs rounded-md"
                    :class="{
                      'bg-yellow-100 text-yellow-800': request.approvalStatus === 'Pending',
                      'bg-blue-100 text-blue-800': request.approvalStatus === 'Approved by HR',
                      'bg-green-100 text-green-800': request.approvalStatus === 'Approved by IT',
                      'bg-red-100 text-red-800': request.approvalStatus === 'Rejected'
                    }"
                  >
                    {{ request.approvalStatus }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">{{ request.createdAt }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="mt-4 flex justify-between items-center">
          <div>
            Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to {{ Math.min(currentPage * itemsPerPage, reportData.length) }} of {{ reportData.length }} results
          </div>
          <div class="flex space-x-1">
            <button
              v-for="page in totalPages"
              :key="page"
              @click="currentPage = page"
              class="px-3 py-1 border rounded"
              :class="currentPage === page ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'"
            >
              {{ page }}
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-10">
        <i class="fas fa-file-alt text-gray-300 text-5xl mb-4"></i>
        <p class="text-gray-500">No access requests found. Try adjusting your filters.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import api from '@/services/apiService';
import Papa from 'papaparse';
import * as XLSX from 'xlsx';

// Props
const props = defineProps(['subsidiary']);

// Store and router
const store = useStore();
const router = useRouter();

// State
const reportData = ref([]);
const loading = ref(false);
const error = ref(null);
const currentPage = ref(1);
const itemsPerPage = ref(10);

// Filter state
const filters = ref({
  startDate: '',
  endDate: '',
  status: '',
  systemName: '',
  department: '',
  role: ''
});

// Computed properties
const uniqueSystems = computed(() => {
  const systems = [...new Set(reportData.value.map(item => item.systemName))];
  return systems.sort();
});

const uniqueDepartments = computed(() => {
  const departments = [...new Set(reportData.value.map(item => item.department))];
  return departments.sort();
});

const uniqueRoles = computed(() => {
  const roles = [...new Set(reportData.value.map(item => item.role))];
  return roles.sort();
});

const totalPages = computed(() => {
  return Math.ceil(reportData.value.length / itemsPerPage.value);
});

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return reportData.value.slice(start, end);
});

// Methods
const fetchReport = async () => {
  loading.value = true;
  error.value = null;

  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (filters.value.startDate) queryParams.append('startDate', filters.value.startDate);
    if (filters.value.endDate) queryParams.append('endDate', filters.value.endDate);
    if (filters.value.status) queryParams.append('status', filters.value.status);
    if (filters.value.systemName) queryParams.append('systemName', filters.value.systemName);
    if (filters.value.department) queryParams.append('department', filters.value.department);
    if (filters.value.role) queryParams.append('role', filters.value.role);

    const response = await api.get(`/access-request/report?${queryParams.toString()}`, {
      headers: {
        'x-subsidiary': props.subsidiary,
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (response.data && response.data.data) {
      reportData.value = response.data.data;
      currentPage.value = 1; // Reset to first page
    } else {
      reportData.value = [];
      error.value = 'No data returned from server';
    }
  } catch (err) {
    console.error('Error fetching report:', err);
    error.value = err.response?.data?.message || err.message || 'Failed to fetch report';
    reportData.value = [];
  } finally {
    loading.value = false;
  }
};

const resetFilters = () => {
  filters.value = {
    startDate: '',
    endDate: '',
    status: '',
    systemName: '',
    department: '',
    role: ''
  };
};

const downloadCSV = () => {
  // Create CSV content
  const csv = Papa.unparse(reportData.value);

  // Create a blob with the CSV content
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

  // Create a URL for the blob
  const url = URL.createObjectURL(blob);

  // Create a link element
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', `access-requests-${props.subsidiary}-${new Date().toISOString().split('T')[0]}.csv`);

  // Append the link to the document
  document.body.appendChild(link);

  // Click the link to trigger the download
  link.click();

  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

const downloadExcel = () => {
  // Create a workbook
  const wb = XLSX.utils.book_new();

  // Create a worksheet
  const ws = XLSX.utils.json_to_sheet(reportData.value);

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(wb, ws, 'Access Requests');

  // Generate Excel file
  XLSX.writeFile(wb, `access-requests-${props.subsidiary}-${new Date().toISOString().split('T')[0]}.xlsx`);
};

// Initialize
onMounted(() => {
  fetchReport();
});
</script>
