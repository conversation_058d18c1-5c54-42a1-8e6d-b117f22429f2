import api, { login as loginApi, verifyToken as verifyToken<PERSON>pi, getCurrentUser } from '@/services/apiService';
import router from '@/router';

// Helper function to set auth token in localStorage and axios headers
const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('token', token);
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    localStorage.removeItem('token');
    delete api.defaults.headers.common['Authorization'];
  }
};

// Initial state
const state = {
  token: localStorage.getItem('token') || null,
  user: JSON.parse(localStorage.getItem('user')) || null,
  subsidiary: localStorage.getItem('selectedSubsidiary') || null,
  loading: false,
  error: null,
  isAuthenticated: !!localStorage.getItem('token')
};

// Mutations
const mutations = {
  AUTH_REQUEST(state) {
    state.loading = true;
    state.error = null;
  },
  AUTH_SUCCESS(state, { token, user }) {
    state.token = token;
    state.user = user;
    state.loading = false;
    state.isAuthenticated = true;
    state.error = null;
  },
  AUTH_ERROR(state, error) {
    state.loading = false;
    state.error = error;
  },
  SET_SUBSIDIARY(state, subsidiary) {
    state.subsidiary = subsidiary;
    localStorage.setItem('selectedSubsidiary', subsidiary);
  },
  LOGOUT(state) {
    state.token = null;
    state.user = null;
    state.isAuthenticated = false;
    state.subsidiary = null;
  },
  CLEAR_ERROR(state) {
    state.error = null;
  }
};

// Actions
const actions = {
  // Clear session data - used when switching subsidiaries or logging out
  async clearSession({ commit }, { keepSubsidiary = false } = {}) {
    console.log('Clearing session data, keepSubsidiary:', keepSubsidiary);

    // AGGRESSIVE APPROACH: Clear all localStorage completely
    // This ensures no lingering tokens or session data
    if (!keepSubsidiary) {
      // Clear everything except theme preferences if needed
      const keys = Object.keys(localStorage);
      for (const key of keys) {
        if (key !== 'theme') { // Keep theme preference if you have one
          localStorage.removeItem(key);
        }
      }
    } else {
      // Clear only auth-related data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }

    // Clear all sessionStorage as well
    sessionStorage.clear();

    // Clear all auth headers
    delete api.defaults.headers.common['Authorization'];

    // Also clear the x-subsidiary header if it exists
    if (api.defaults.headers.common['x-subsidiary']) {
      delete api.defaults.headers.common['x-subsidiary'];
    }

    // Reset store state
    commit('LOGOUT');

    // Clear any cookies related to authentication
    document.cookie.split(";").forEach(function(c) {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });

    return true;
  },

  // Login action
  async login({ commit, dispatch, state }, { email, password, subsidiary }) {
    try {
      // ALWAYS clear the session before login to prevent cross-subsidiary issues
      console.log(`Auth store: Preparing to log in to ${subsidiary}`);

      // Force a complete logout to clear all session data
      await dispatch('clearSession', { keepSubsidiary: false });

      // Clear localStorage completely (except theme)
      const keys = Object.keys(localStorage);
      for (const key of keys) {
        if (key !== 'theme') {
          localStorage.removeItem(key);
        }
      }

      // Clear sessionStorage
      sessionStorage.clear();

      // Clear cookies
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });

      // Double-check that auth headers are cleared
      if (api.defaults.headers.common['Authorization']) {
        console.log('Auth store: Clearing lingering Authorization header');
        delete api.defaults.headers.common['Authorization'];
      }

      // Double-check that subsidiary header is cleared
      if (api.defaults.headers.common['x-subsidiary']) {
        console.log('Auth store: Clearing lingering x-subsidiary header');
        delete api.defaults.headers.common['x-subsidiary'];
      }

      console.log('Auth store: Session completely cleared before login');

      commit('AUTH_REQUEST');

      console.log(`API Request: Using subsidiary ${subsidiary} (requested: ${subsidiary}, user: undefined)`);

      // Attempt login with the provided subsidiary
      const response = await loginApi({ email, password, subsidiary }, subsidiary);
      const { token, data } = response.data;

      // Get the user's actual subsidiary from the response
      const userSubsidiary = data.user.sub;

      // Check if the user's subsidiary matches the requested one
      if (userSubsidiary && userSubsidiary !== subsidiary) {
        console.warn(`User subsidiary mismatch: User belongs to ${userSubsidiary}, tried to access ${subsidiary}`);

        // Special case for premierkenya and premiergroup - they are the same entity
        if ((userSubsidiary === 'premierkenya' && subsidiary === 'premiergroup') ||
            (userSubsidiary === 'premiergroup' && subsidiary === 'premierkenya')) {
          console.log('Special case in auth store: premierkenya and premiergroup are treated as the same entity');

          // For users with premiergroup subsidiary trying to access premierkenya
          if (userSubsidiary === 'premiergroup' && subsidiary === 'premierkenya') {
            console.log('User from premiergroup accessing premierkenya - updating user subsidiary in auth store');

            // Update the user object to use premierkenya as the subsidiary
            data.user.sub = 'premierkenya';
          }

          // Use the requested subsidiary in this special case
          console.log(`Using requested subsidiary: ${subsidiary}`);
        } else {
          // Use the user's actual subsidiary instead of the requested one
          subsidiary = userSubsidiary;

          // Show a warning in the console to help with debugging
          console.warn(`Automatically switching to user's actual subsidiary: ${userSubsidiary}`);
        }
      }

      // Save the correct subsidiary to localStorage
      localStorage.setItem('selectedSubsidiary', subsidiary);
      commit('SET_SUBSIDIARY', subsidiary);

      // Store user data and token
      localStorage.setItem('user', JSON.stringify(data.user));
      setAuthToken(token);

      commit('AUTH_SUCCESS', {
        token,
        user: data.user
      });

      // Check if user needs to change password
      if (data.user.requirePasswordChange) {
        console.log('User needs to change password, will redirect to password reset page');
        // The router navigation guard will handle the redirect to the password reset page
      }

      return { success: true, data: data };
    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = 'Authentication failed';

      // Handle specific error cases
      if (error.response?.status === 403 && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.status === 'fail' && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      commit('AUTH_ERROR', errorMessage);
      setAuthToken(null);
      return { success: false, error: errorMessage };
    }
  },

  // Logout action
  async logout({ dispatch }) {
    console.log('Logout action called');

    try {
      // Use the clearSession action to clean up all auth data
      // We must clear the subsidiary to prevent cross-subsidiary authentication issues
      await dispatch('clearSession', { keepSubsidiary: false });

      // IMPORTANT: Force a complete page reload to clear any cached tokens
      // Add a 'fresh=true' parameter to indicate this is a fresh login after logout
      // This helps the login page know to start with a completely clean state

      // ALWAYS use the /user-access/ui/login path regardless of environment
      // This ensures consistency between development and production
      const loginUrl = '/user-access/ui/login?fresh=true';
      console.log('Using login URL:', loginUrl);

      // Log the redirect URL for debugging
      console.log('Redirecting to:', loginUrl);

      // Use router.push for SPA navigation when in development
      if (process.env.NODE_ENV === 'development') {
        router.push('/login?fresh=true');
      } else {
        // For production, use window.location to ensure a full page reload
        // This is more reliable in production environments
        window.location.href = loginUrl;
      }

    } catch (error) {
      console.error('Error during logout:', error);

      // Fallback to the same URL pattern if there's an error
      if (process.env.NODE_ENV === 'development') {
        router.push('/login?fresh=true');
      } else {
        window.location.href = '/user-access/ui/login?fresh=true';
      }
    }
  },

  // Verify token action
  async verifyToken({ commit, state }) {
    if (!state.token) return { success: false };

    try {
      commit('AUTH_REQUEST');

      const response = await verifyTokenApi(state.token, state.subsidiary);

      const { data } = response.data;

      // Update user data
      localStorage.setItem('user', JSON.stringify(data.user));

      commit('AUTH_SUCCESS', {
        token: state.token,
        user: data.user
      });

      // Check if user needs to change password
      if (data.user.requirePasswordChange) {
        console.log('verifyToken: User needs to change password, will redirect to password reset page');
        // The router navigation guard will handle the redirect to the password reset page
      }

      return { success: true, data };
    } catch (error) {
      commit('AUTH_ERROR', error.response?.data?.message || 'Token verification failed');
      commit('LOGOUT');
      setAuthToken(null);
      return { success: false, error: error.response?.data?.message || 'Token verification failed' };
    }
  },

  // Clear error action
  clearError({ commit }) {
    commit('CLEAR_ERROR');
  },

  // Set subsidiary action
  setSubsidiary({ commit }, subsidiary) {
    commit('SET_SUBSIDIARY', subsidiary);
  },

  // Fetch current user
  async fetchCurrentUser({ commit, state }) {
    if (!state.token || !state.subsidiary) return { success: false };

    try {
      commit('AUTH_REQUEST');

      const response = await getCurrentUser(state.subsidiary);
      const { data } = response.data;

      // Update user data
      localStorage.setItem('user', JSON.stringify(data.user));

      commit('AUTH_SUCCESS', {
        token: state.token,
        user: data.user
      });

      // Check if user needs to change password
      if (data.user.requirePasswordChange) {
        console.log('fetchCurrentUser: User needs to change password, will redirect to password reset page');
        // The router navigation guard will handle the redirect to the password reset page
      }

      return { success: true, data };
    } catch (error) {
      commit('AUTH_ERROR', error.response?.data?.message || 'Failed to fetch user data');
      return { success: false, error: error.response?.data?.message || 'Failed to fetch user data' };
    }
  }
};

// Getters
const getters = {
  isAuthenticated: state => state.isAuthenticated,
  currentUser: state => state.user,
  authLoading: state => state.loading,
  authError: state => state.error,
  currentSubsidiary: state => state.subsidiary,
  hasToken: state => !!state.token
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
