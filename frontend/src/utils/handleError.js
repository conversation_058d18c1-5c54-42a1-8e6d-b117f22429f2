export const handleError = (error) => {
  const sqlErrors = [
    "SequelizeUniqueConstraintError",
    "SequelizeConnectionRefusedError",
    "SequelizeConnectionTimedOutError",
    "SequelizeForeignKeyConstraintError",
  ];

  if (!error) {
    return "Something went wrong";
  }

  const errorData = error.data || error;

  if (sqlErrors.includes(errorData.type)) {
    return errorData.message.sqlMessage;
  } else {
    return error && errorData.message;
  }
};
