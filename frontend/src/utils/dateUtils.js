/**
 * Timezone configuration for each subsidiary
 */
const timezones = {
  // Kenya subsidiaries (EAT - East Africa Time, UTC+3)
  platinumkenya: {
    timezone: 'Africa/Nairobi',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },
  premierkenya: {
    timezone: 'Africa/Nairobi',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },
  momentumcredit: {
    timezone: 'Africa/Nairobi',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },

  // Tanzania subsidiaries (EAT - East Africa Time, UTC+3)
  platinumtanzania: {
    timezone: 'Africa/Dar_es_Salaam',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },
  premierfanikiwa: {
    timezone: 'Africa/Dar_es_Salaam',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },

  // Uganda subsidiaries (EAT - East Africa Time, UTC+3)
  platinumuganda: {
    timezone: 'Africa/Kampala',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },
  premieruganda: {
    timezone: 'Africa/Kampala',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },

  // Zambia subsidiary (CAT - Central Africa Time, UTC+2)
  spectrumzambia: {
    timezone: 'Africa/Lusaka',
    utcOffset: '+02:00',
    name: 'Central Africa Time'
  },

  // South Africa subsidiary (SAST - South Africa Standard Time, UTC+2)
  premiersouthafrica: {
    timezone: 'Africa/Johannesburg',
    utcOffset: '+02:00',
    name: 'South Africa Standard Time'
  },

  // Default fallback (Kenya time)
  default: {
    timezone: 'Africa/Nairobi',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  }
};

/**
 * Format date with correct timezone for subsidiary
 * @param {Date|string} date - The date to format
 * @param {string} subsidiary - The subsidiary identifier
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date string
 */
export const formatDateForSubsidiary = (date, subsidiary = 'platinumkenya', options = {}) => {
  try {
    const dateObj = new Date(date);
    const timezoneConfig = timezones[subsidiary] || timezones.default;
    
    const defaultOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: timezoneConfig.timezone,
      hour12: false // Use 24-hour format
    };

    const formatOptions = { ...defaultOptions, ...options };
    
    return dateObj.toLocaleString('en-US', formatOptions);
  } catch (error) {
    console.error('Error formatting date:', error);
    return new Date(date).toLocaleString();
  }
};

/**
 * Get current date/time for a specific subsidiary
 * @param {string} subsidiary - The subsidiary identifier
 * @returns {string} Current date/time formatted for the subsidiary
 */
export const getCurrentTimeForSubsidiary = (subsidiary = 'platinumkenya') => {
  return formatDateForSubsidiary(new Date(), subsidiary);
};

/**
 * Format date for display in tables and lists
 * @param {Date|string} date - The date to format
 * @param {string} subsidiary - The subsidiary identifier
 * @returns {string} Formatted date string
 */
export const formatDateForDisplay = (date, subsidiary = 'platinumkenya') => {
  return formatDateForSubsidiary(date, subsidiary, {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Get timezone info for a subsidiary
 * @param {string} subsidiary - The subsidiary identifier
 * @returns {Object} Timezone configuration
 */
export const getTimezoneInfo = (subsidiary = 'platinumkenya') => {
  return timezones[subsidiary] || timezones.default;
};
