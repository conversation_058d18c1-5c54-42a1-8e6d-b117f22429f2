import { createRouter, createWebHistory } from "vue-router";
import UserLogin from "@/views/UserLogin.vue";
import Dashboard from "@/views/Dashboard.vue"; // Updated to new Dashboard component
import UserDashboard from "@/views/UserDashboard.vue"; // Keep the old one for reference
import RequestForm from "@/views/RequestForm.vue";
import Approval from "@/views/ApprovalRequests.vue";
import AuditLog from "@/views/AuditLog.vue";
import Reports from "@/views/UserReports.vue";
import VerifyLogin from "@/views/VerifyLogin.vue";
import UserManagement from "@/views/UserManagement.vue";
import PasswordResetPage from "@/views/PasswordResetPage.vue";
// import TenantManagement from "@/views/TenantManagement.vue";
import store from "@/store";
import { canAccess } from "@/utils/accessControl";

// Import Revocation Components
import RevokeForm from "@/views/RevokeForm.vue";
import AccessRemoval from "@/views/AccessRemoval.vue";
import RevocationAudit from "@/views/RevocationAudit.vue";
import RevokedUsersReport from "@/views/RevokedUsersReport.vue";

// Import Access Request Report
import AccessRequestReport from "@/views/AccessRequestReport.vue";

const routes = [
  // Auth routes
  {
    path: "/",
    name: "Home",
    component: UserLogin,
    meta: { requiresAuth: false }
  },
  {
    path: "/login",
    name: "Login",
    component: UserLogin,
    meta: { requiresAuth: false }
  },
  {
    path: "/user-access/ui/login",
    name: "ProductionLogin",
    component: UserLogin,
    meta: { requiresAuth: false }
  },
  {
    path: "/user-access/ui/",
    redirect: "/",
    meta: { requiresAuth: false }
  },
  {
    path: "/password-reset",
    name: "PasswordReset",
    component: PasswordResetPage,
    meta: { requiresAuth: true, isPasswordReset: true }
  },
  {
    path: "/user-access/ui/password-reset",
    name: "ProductionPasswordReset",
    component: PasswordResetPage,
    meta: { requiresAuth: true, isPasswordReset: true }
  },
  {
    path: "/user-access/ui/dashboard/:subsidiary",
    redirect: to => {
      // Redirect to the internal dashboard route with the subsidiary parameter
      return `/dashboard/${to.params.subsidiary}`;
    },
    meta: { requiresAuth: true }
  },

  // Protected routes
  {
    path: "/dashboard/:subsidiary",
    component: Dashboard,
    props: true,
    meta: { requiresAuth: true }
  },
  {
    path: "/request/:subsidiary",
    component: RequestForm,
    props: true,
    meta: { requiresAuth: true, resource: 'request-form' }
  },
  {
    path: "/approval/:subsidiary",
    component: Approval,
    props: true,
    meta: { requiresAuth: true, resource: 'approval' }
  },
  {
    path: "/audit/:subsidiary",
    component: AuditLog,
    props: true,
    meta: { requiresAuth: true, resource: 'audit' }
  },
  {
    path: "/users/:subsidiary",
    component: UserManagement,
    props: true,
    meta: { requiresAuth: true, resource: 'users' }
  },
  // {
  //   path: "/tenants/:subsidiary",
  //   component: TenantManagement,
  //   props: true,
  //   meta: { requiresAuth: true, resource: 'tenants' }
  // },
  {
    path: "/reports/:subsidiary",
    component: Reports,
    props: true,
    meta: { requiresAuth: true, resource: 'reports' }
  },

  // Revocation Section Routes
  {
    path: "/revoke-form/:subsidiary",
    component: RevokeForm,
    props: true,
    meta: { requiresAuth: true, resource: 'revoke-form' }
  },
  {
    path: "/access-removal/:subsidiary",
    component: AccessRemoval,
    props: true,
    meta: { requiresAuth: true, resource: 'access-removal' }
  },
  {
    path: "/revocation-audit/:subsidiary",
    component: RevocationAudit,
    props: true,
    meta: { requiresAuth: true, resource: 'revocation-audit' }
  },
  {
    path: "/revoked-users/:subsidiary",
    component: RevokedUsersReport,
    props: true,
    meta: { requiresAuth: true, resource: 'revoked-users-report' }
  },

  // Access Request Report Route
  {
    path: "/access-request-report/:subsidiary",
    component: AccessRequestReport,
    props: true,
    meta: { requiresAuth: true, resource: 'reports' } // Using 'reports' resource to match existing permissions
  },
  // wild card route
  {
    path: "/:pathMatch(.*)*",
    component: UserLogin,
    props: (route) => ({
      email: route.query.email,
      eid: route.query.eid,
      sub: route.query.sub,
    }),
    meta: { requiresAuth: false }
  },
];

// Import config to get the base URL
import config from '@/config';

// Get the base URL from the config
const baseUrl = '/user-access/ui';

// Create the router with the correct base URL
const router = createRouter({
  history: createWebHistory(baseUrl),
  routes,
});

// Navigation guard to check authentication, subsidiary access, and role-based access
router.beforeEach((to, _, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const isAuthenticated = store.getters['auth/isAuthenticated'];
  const currentUser = store.getters['auth/currentUser'];
  const userSubsidiary = currentUser?.sub;
  const requestedSubsidiary = to.params.subsidiary;

  // Check if user needs to change password
  const requiresPasswordChange = currentUser?.requirePasswordChange === true;
  console.log('Router navigation guard - requiresPasswordChange:', requiresPasswordChange, 'currentUser:', currentUser);

  // If route requires auth and user is not authenticated
  if (requiresAuth && !isAuthenticated) {
    console.log('Authentication required, redirecting to login');

    // ALWAYS use the /user-access/ui/login path regardless of environment
    // This ensures consistency between development and production
    const loginPath = '/user-access/ui/login';
    console.log('Using login path for redirect:', loginPath);

    next(loginPath);
    return;
  }

  // Check if user needs to change password
  if (requiresAuth && isAuthenticated && requiresPasswordChange) {
    // If the user is already on the password reset page or login page, allow it
    if (to.meta.isPasswordReset || to.path === '/login' || to.path === '/user-access/ui/login' || to.path === '/') {
      console.log('User is on password reset or login page, allowing access');
      next();
      return;
    }

    console.warn('User needs to change password, redirecting to password reset page');
    console.log('Current user:', currentUser);

    // Redirect to the dedicated password reset page
    next('/password-reset');
    return;
  }

  // If user is trying to access password reset page but doesn't need to change password
  if (to.meta.isPasswordReset && isAuthenticated && !requiresPasswordChange) {
    console.log('User does not need to change password, redirecting to dashboard');
    next(`/dashboard/${userSubsidiary || 'platinumkenya'}`);
    return;
  }

  // Check if user has access to the requested subsidiary
  if (requiresAuth && requestedSubsidiary && userSubsidiary && requestedSubsidiary !== userSubsidiary) {
    console.warn(`User ${currentUser?.username} with subsidiary ${userSubsidiary} denied access to ${requestedSubsidiary}`);

    // Show an alert to the user
    alert(`You do not have access to the ${requestedSubsidiary} subsidiary. You only have access to ${userSubsidiary}.`);

    // Redirect to the user's authorized subsidiary dashboard
    next(`/dashboard/${userSubsidiary}`);
    return;
  }

  // Check for role-based access using the resource metadata
  const resource = to.meta.resource;
  if (requiresAuth && resource && currentUser) {
    // If the user doesn't have access to this resource
    if (!canAccess(resource, currentUser)) {
      console.warn(`User ${currentUser.username} with role ${currentUser.role} denied access to ${resource}`);
      // Redirect to dashboard with the user's subsidiary
      next(`/dashboard/${userSubsidiary || 'platinumkenya'}`);
      return;
    }
  }

  // Allow navigation
  next();
});

export default router;
