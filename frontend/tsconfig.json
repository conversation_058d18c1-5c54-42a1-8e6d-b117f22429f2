{
  "compilerOptions": {
    "strict":false,
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["DOM", "ESNext"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "paths": {
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "types": [
      "vite/client",
      "vite-plugin-vue-layouts/client",
      "unplugin-vue-router/client"
    ],
    "allowJs": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true,
    "noImplicitAny": false
  },
  "include": [
    "src/**/*",
    "src/**/*.vue"
  ],
  "exclude": ["dist", "node_modules", "cypress"],
  "files": [],
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" },
  ],
}
