const { Revocation } = require('./src/db/models');

async function testRevocations() {
  try {
    console.log('Testing revocation database operations...');
    
    // Check existing revocations
    const existing = await Revocation.findAll();
    console.log(`Found ${existing.length} existing revocations:`);
    existing.forEach(rev => {
      console.log(`- ID: ${rev.id}, Name: ${rev.fullName}, Status: ${rev.status}, Subsidiary: ${rev.subsidiary}`);
    });
    
    // Create a test revocation
    console.log('\nCreating test revocation...');
    const testRevocation = await Revocation.create({
      fullName: 'Test User',
      employeeId: 'TEST001',
      jobTitle: 'Test Position',
      department: 'Test Department',
      contact: '+254700000000',
      systems: ['Test System'],
      reason: 'Resignation/Termination',
      approverName: 'Test Approver',
      approverJobTitle: 'HR',
      approverContact: '<EMAIL>',
      subsidiary: 'platinumkenya',
      status: 'Approved by HR',
      submittedBy: 'HR'
    });
    
    console.log('✅ Test revocation created successfully!');
    console.log(`ID: ${testRevocation.id}, Status: ${testRevocation.status}`);
    
    // Check revocations with "Approved by HR" status
    const approvedByHR = await Revocation.findAll({
      where: { status: 'Approved by HR', subsidiary: 'platinumkenya' }
    });
    
    console.log(`\nFound ${approvedByHR.length} revocations with "Approved by HR" status for platinumkenya:`);
    approvedByHR.forEach(rev => {
      console.log(`- ID: ${rev.id}, Name: ${rev.fullName}, Status: ${rev.status}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
  }
  
  process.exit(0);
}

testRevocations();
