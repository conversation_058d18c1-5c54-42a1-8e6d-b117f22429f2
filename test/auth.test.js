const chai = require('chai');
const chaiHttp = require('chai-http');
const app = require('../src/app');
const expect = chai.expect;

chai.use(chaiHttp);

describe('Authentication', () => {
  // Test variables
  let token;
  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
    username: 'testuser',
    sub: 'platinumkenya',
    employerId: 'EMP123'
  };

  // Clean up before tests
  before(async () => {
    try {
      // You might want to add code to clean up test data before running tests
    } catch (error) {
      console.error('Error in test setup:', error);
    }
  });

  // Test registration
  describe('POST /user-access/v1/auth/register', () => {
    it('should register a new user', (done) => {
      chai
        .request(app)
        .post('/user-access/v1/auth/register')
        .send(testUser)
        .end((err, res) => {
          expect(res).to.have.status(201);
          expect(res.body).to.have.property('status').equal('success');
          expect(res.body).to.have.property('token');
          expect(res.body.data).to.have.property('user');
          expect(res.body.data.user).to.have.property('email').equal(testUser.email);
          token = res.body.token; // Save token for later tests
          done();
        });
    });

    it('should not register a user with an existing email', (done) => {
      chai
        .request(app)
        .post('/user-access/v1/auth/register')
        .send(testUser)
        .end((err, res) => {
          expect(res).to.have.status(400);
          expect(res.body).to.have.property('status').equal('fail');
          done();
        });
    });
  });

  // Test login
  describe('POST /user-access/v1/auth/login', () => {
    it('should login a user with valid credentials', (done) => {
      chai
        .request(app)
        .post('/user-access/v1/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .end((err, res) => {
          expect(res).to.have.status(200);
          expect(res.body).to.have.property('status').equal('success');
          expect(res.body).to.have.property('token');
          done();
        });
    });

    it('should not login a user with invalid credentials', (done) => {
      chai
        .request(app)
        .post('/user-access/v1/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .end((err, res) => {
          expect(res).to.have.status(401);
          expect(res.body).to.have.property('status').equal('fail');
          done();
        });
    });
  });

  // Test token verification
  describe('POST /user-access/v1/auth/verify-token', () => {
    it('should verify a valid token', (done) => {
      chai
        .request(app)
        .post('/user-access/v1/auth/verify-token')
        .send({ token })
        .end((err, res) => {
          expect(res).to.have.status(200);
          expect(res.body).to.have.property('status').equal('success');
          expect(res.body.data).to.have.property('user');
          expect(res.body.data.user).to.have.property('email').equal(testUser.email);
          done();
        });
    });

    it('should not verify an invalid token', (done) => {
      chai
        .request(app)
        .post('/user-access/v1/auth/verify-token')
        .send({ token: 'invalid-token' })
        .end((err, res) => {
          expect(res).to.have.status(401);
          expect(res.body).to.have.property('status').equal('fail');
          done();
        });
    });
  });

  // Test protected route
  describe('GET /user-access/v1/auth/me', () => {
    it('should access protected route with valid token', (done) => {
      chai
        .request(app)
        .get('/user-access/v1/auth/me')
        .set('Authorization', `Bearer ${token}`)
        .end((err, res) => {
          expect(res).to.have.status(200);
          expect(res.body).to.have.property('status').equal('success');
          expect(res.body.data).to.have.property('user');
          expect(res.body.data.user).to.have.property('email').equal(testUser.email);
          done();
        });
    });

    it('should not access protected route without token', (done) => {
      chai
        .request(app)
        .get('/user-access/v1/auth/me')
        .end((err, res) => {
          expect(res).to.have.status(401);
          expect(res.body).to.have.property('status').equal('fail');
          done();
        });
    });
  });

  // Clean up after tests
  after(async () => {
    try {
      // You might want to add code to clean up test data after running tests
    } catch (error) {
      console.error('Error in test cleanup:', error);
    }
  });
});
