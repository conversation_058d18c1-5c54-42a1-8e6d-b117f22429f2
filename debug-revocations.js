const { Revocation } = require('./src/db/models');

async function debugRevocations() {
  try {
    console.log('🔍 Debugging revocation system...\n');
    
    // 1. Check all existing revocations
    console.log('1. Checking all existing revocations:');
    const allRevocations = await Revocation.findAll({
      order: [['createdAt', 'DESC']]
    });
    console.log(`   Found ${allRevocations.length} total revocations`);
    
    if (allRevocations.length > 0) {
      console.log('   Recent revocations:');
      allRevocations.slice(0, 5).forEach((rev, index) => {
        console.log(`   ${index + 1}. ID: ${rev.id}, Name: ${rev.fullName}, Status: "${rev.status}", Subsidiary: ${rev.subsidiary}, Created: ${rev.createdAt}`);
      });
    }
    
    // 2. Check revocations with "Approved by HR" status
    console.log('\n2. Checking revocations with "Approved by HR" status:');
    const approvedByHR = await Revocation.findAll({
      where: { status: 'Approved by HR' },
      order: [['createdAt', 'DESC']]
    });
    console.log(`   Found ${approvedByHR.length} revocations with "Approved by HR" status`);
    
    if (approvedByHR.length > 0) {
      approvedByHR.forEach((rev, index) => {
        console.log(`   ${index + 1}. ID: ${rev.id}, Name: ${rev.fullName}, Subsidiary: ${rev.subsidiary}`);
      });
    }
    
    // 3. Check revocations for specific subsidiary
    console.log('\n3. Checking revocations for platinumkenya subsidiary:');
    const platinumRevocations = await Revocation.findAll({
      where: { subsidiary: 'platinumkenya' },
      order: [['createdAt', 'DESC']]
    });
    console.log(`   Found ${platinumRevocations.length} revocations for platinumkenya`);
    
    if (platinumRevocations.length > 0) {
      platinumRevocations.forEach((rev, index) => {
        console.log(`   ${index + 1}. ID: ${rev.id}, Name: ${rev.fullName}, Status: "${rev.status}"`);
      });
    }
    
    // 4. Check what the API endpoint should return
    console.log('\n4. Checking what /revocation/pending should return for platinumkenya:');
    const pendingForPlatinum = await Revocation.findAll({
      where: { 
        status: 'Approved by HR',
        subsidiary: 'platinumkenya'
      },
      order: [['createdAt', 'DESC']]
    });
    console.log(`   Should return ${pendingForPlatinum.length} revocations`);
    
    if (pendingForPlatinum.length > 0) {
      pendingForPlatinum.forEach((rev, index) => {
        console.log(`   ${index + 1}. ID: ${rev.id}, Name: ${rev.fullName}, Employee ID: ${rev.employeeId}`);
      });
    }
    
    // 5. Create a test revocation if none exist
    if (pendingForPlatinum.length === 0) {
      console.log('\n5. Creating a test revocation...');
      const testRevocation = await Revocation.create({
        fullName: 'Test Employee',
        employeeId: 'TEST123',
        jobTitle: 'Test Position',
        department: 'Test Department',
        contact: '+254700000000',
        systems: ['Test System', 'Email'],
        reason: 'Resignation/Termination',
        approverName: 'HR Manager',
        approverJobTitle: 'HR',
        approverContact: '<EMAIL>',
        subsidiary: 'platinumkenya',
        status: 'Approved by HR',
        submittedBy: 'HR'
      });
      
      console.log(`   ✅ Created test revocation with ID: ${testRevocation.id}`);
      console.log(`   Status: "${testRevocation.status}"`);
      console.log(`   Subsidiary: ${testRevocation.subsidiary}`);
      
      // Verify it can be found
      const verification = await Revocation.findAll({
        where: { 
          status: 'Approved by HR',
          subsidiary: 'platinumkenya'
        }
      });
      console.log(`   ✅ Verification: Now ${verification.length} revocations should appear in Access Removal page`);
    }
    
    console.log('\n🎯 Summary:');
    console.log(`   - Total revocations: ${allRevocations.length}`);
    console.log(`   - "Approved by HR" status: ${approvedByHR.length}`);
    console.log(`   - Platinumkenya subsidiary: ${platinumRevocations.length}`);
    console.log(`   - Should appear in Access Removal: ${pendingForPlatinum.length}`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
  }
  
  process.exit(0);
}

debugRevocations();
