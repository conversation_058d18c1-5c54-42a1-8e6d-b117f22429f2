#!/bin/bash
# --- Safety Settings ---
set -euo pipefail
trap 'echo "❌ Error on line $LINENO. Exiting..."; exit 1' ERR

# --- Debugging (comment out in production) ---
set -x  # Enable debugging output

# --- Docker and AWS ECR Variables ---
AWS_REGION="eu-central-1"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
ECR_REPO_URL="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/user-access-app-repo"
DOCKER_COMPOSE_PATH="/var/User-Access-App/docker-compose.yml"
APP_NAME="User-Access-App"

# --- AWS ECR Login ---
echo "🔑 Logging into AWS ECR..."
aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$ECR_REPO_URL"


# --- Configurable SNS Topic ---
SNS_TOPIC_ARN="arn:aws:sns:${AWS_REGION}:${AWS_ACCOUNT_ID}:DevSecOps-Alert"

check_health_and_get_logs_if_unhealthy() {
    local container_id
    container_id=$(docker ps -q --filter "name=${APP_NAME}")

    if [[ -z "$container_id" ]]; then
        echo "No container found for $APP_NAME"
        echo "🛑 No container running" && return
    fi

    local health_status
    health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_id" 2>/dev/null || echo "unavailable")

    if [[ "$health_status" == "unhealthy" ]]; then
        echo "❗ Container is unhealthy. Collecting logs..."
        echo "⚠️ Container Health: $health_status"
        echo "--- Latest Logs ---"
        docker logs --tail 20 "$container_id"
    else
        echo "✅ Container Health: $health_status"
    fi
}

notify_success() {
    local status_message container_health_log
    status_message=$(get_running_containers_status)
    container_health_log=$(check_health_and_get_logs_if_unhealthy)

    aws sns publish \
        --topic-arn "$SNS_TOPIC_ARN" \
        --subject "✅ Container Deployment Successful - $APP_NAME" \
        --message "Deployment completed successfully with image: $IMAGE_URI

Running Containers:
$status_message

Health & Logs:
$container_health_log" \
        --region "$AWS_REGION"
}



notify_failure() {
    local error_message="$1"
    echo "❌ $error_message" >&2
    aws sns publish \
        --topic-arn "$SNS_TOPIC_ARN" \
        --subject "❌ Container Deployment Failed - $APP_NAME" \
        --message "$error_message" \
        --region "$AWS_REGION" || true
    exit 1
}

# --- Fetch the Latest Image from ECR ---
echo "🔄 Fetching the latest image pushed today..."
LATEST_IMAGE_TAG=$(aws ecr describe-images \
    --repository-name "user-access-app-repo" \
    --region "$AWS_REGION" \
    --query "sort_by(imageDetails[?imageTags!=null && imagePushedAt>=\`$(date -u +%Y-%m-%d)T00:00:00Z\`], &imagePushedAt)[-1].imageTags" \
    --output text | head -n 1 | tr -d '\n\r')

# Handle missing image
if [[ -z "$LATEST_IMAGE_TAG" || "$LATEST_IMAGE_TAG" == "None" ]]; then
    echo "⚠️ No new image found today. Fetching the most recent image..."
    LATEST_IMAGE_TAG=$(aws ecr describe-images \
        --repository-name "user-access-app-repo" \
        --region "$AWS_REGION" \
        --query "sort_by(imageDetails[?imageTags!=null], &imagePushedAt)[-1].imageTags" \
        --output text | head -n 1 | tr -d '\n\r')

    if [[ -z "$LATEST_IMAGE_TAG" || "$LATEST_IMAGE_TAG" == "None" ]]; then
        echo "❌ ERROR: No valid images found in the repository!"
        exit 1
    fi
fi

IMAGE_URI="$ECR_REPO_URL:$LATEST_IMAGE_TAG"
echo "✅ Using latest image: $IMAGE_URI"

# --- Stop and Remove Existing Containers for the Specific App ---
echo "🛑 Stopping and removing old containers for $APP_NAME..."
docker ps -q --filter "name=${APP_NAME}" | xargs -r docker stop

docker ps -aq --filter "name=${APP_NAME}" | xargs -r docker rm

# --- Remove Old Images ---
echo "🧹 Cleaning up old images..."
docker rmi -f $(docker images --filter=reference="${ECR_REPO_URL}:*" -q) || true

# --- Update docker-compose.yml Image Tag for the App Service Only ---
echo "🔄 Updating docker-compose.yml with the latest app image..."
sed -i '/^\s*app:/,/^\s*dev-db:/ {s|^\(\s*image:\s*\).*|\1'"${IMAGE_URI}"'|}' "$DOCKER_COMPOSE_PATH"


# --- Deploy with Docker Compose ---
echo "🚀 Deploying application using docker-compose..."
docker-compose -f "$DOCKER_COMPOSE_PATH" up -d --force-recreate --remove-orphans

# --- Show Running Containers ---
echo "📌 Running containers:"
docker ps

echo "✅ Deployment completed successfully!"
notify_success
