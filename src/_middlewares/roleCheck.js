/**
 * Role-Based Access Control Middleware
 * Checks if the authenticated user has the required role(s) to access a resource
 *
 * @param {string|string[]} roles - A single role or array of roles that are allowed to access the resource
 * @returns {Function} Express middleware function
 */
const roleCheck = (roles) => {
  // Convert single role to array for consistent handling
  const allowedRoles = Array.isArray(roles) ? roles : [roles];

  return (req, res, next) => {
    try {
      // Check if user exists in request (set by authJwt middleware)
      if (!req.user) {
        return res.status(401).json({
          status: 'fail',
          message: 'You are not logged in. Please log in to get access.'
        });
      }

      // Check if user is active
      if (req.user.status === 'inactive') {
        return res.status(403).json({
          status: 'fail',
          message: 'Your account is inactive. Please contact an administrator.'
        });
      }

      // systemAdmin role has access to everything
      if (req.user.role === 'systemAdmin') {
        return next();
      }

      // Check if user has one of the required roles
      if (!allowedRoles.includes(req.user.role)) {
        return res.status(403).json({
          status: 'fail',
          message: 'You do not have permission to perform this action'
        });
      }

      // User has required role, proceed to next middleware
      next();
    } catch (err) {
      console.error('Role check error:', err);
      return res.status(500).json({
        status: 'error',
        message: 'An error occurred while checking permissions'
      });
    }
  };
};

module.exports = roleCheck;
