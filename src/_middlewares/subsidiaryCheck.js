const { Tenant } = require("../db/models");

/**
 * Middleware to check if the user has access to the requested subsidiary
 * This middleware should be used after the JWT authentication middleware
 */
const subsidiaryCheck = async (req, res, next) => {
  try {
    // Skip if no user is authenticated yet (for login/register routes)
    if (!req.user) {
      return next();
    }

    // Get the subsidiary from the request headers
    const requestedSubsidiary = req.headers["x-subsidiary"]?.toLowerCase();
    
    // If no subsidiary is specified in the headers, continue
    if (!requestedSubsidiary) {
      return next();
    }

    // Get the user's subsidiary from the JWT token
    const userSubId = req.user.subId;
    
    // Find the tenant for the user
    const userTenant = await Tenant.findOne({
      where: { id: userSubId },
    });

    // If user's tenant not found, this is an error
    if (!userTenant) {
      console.error(`User ${req.user.id} has invalid tenant ID: ${userSubId}`);
      return res.status(403).json({
        status: "fail",
        message: "Your account is not associated with a valid subsidiary",
      });
    }

    // Check if the user's subsidiary matches the requested subsidiary
    if (userTenant.sub !== requestedSubsidiary) {
      console.log(`Subsidiary access denied: User belongs to ${userTenant.sub}, tried to access ${requestedSubsidiary}`);
      return res.status(403).json({
        status: "fail",
        message: `You do not have access to the ${requestedSubsidiary} subsidiary. You only have access to ${userTenant.sub}.`,
      });
    }

    // User has access to the requested subsidiary
    next();
  } catch (error) {
    console.error("Subsidiary check error:", error);
    return res.status(500).json({
      status: "error",
      message: "An error occurred while checking subsidiary access",
    });
  }
};

module.exports = subsidiaryCheck;
