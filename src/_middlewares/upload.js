const multer = require("multer");
const fs = require("fs");
const path = require("path");

// Ensure directory exists with proper permissions
const ensureDir = (dirPath) => {
  try {
    if (!fs.existsSync(dirPath)) {
      console.log(`Creating directory: ${dirPath}`);
      fs.mkdirSync(dirPath, { recursive: true, mode: 0o755 });
    } else {
      console.log(`Directory already exists: ${dirPath}`);
      // Check if directory is writable
      try {
        fs.accessSync(dirPath, fs.constants.W_OK);
        console.log(`Directory is writable: ${dirPath}`);
      } catch (error) {
        console.error(`Directory is not writable: ${dirPath}`, error);
        // Try to fix permissions
        try {
          fs.chmodSync(dirPath, 0o755);
          console.log(`Updated permissions for directory: ${dirPath}`);
        } catch (chmodError) {
          console.error(`Failed to update permissions: ${dirPath}`, chmodError);
        }
      }
    }
  } catch (error) {
    console.error(`Error ensuring directory exists: ${dirPath}`, error);
    throw error;
  }
};

// ========== Signature Storage ==========
const signatureStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const dir = "uploads/";
    ensureDir(dir);
    cb(null, dir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, "signature-" + uniqueSuffix + ext);
  },
});

// Signature file filter
const imageFilter = (req, file, cb) => {
  const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error("Only image files are allowed (JPG, JPEG, PNG)."), false);
  }
};

// ========== Excel Storage ==========
const excelStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Create a more specific directory path that includes the subsidiary
    const subsidiary = req.body.subsidiary || req.headers["x-subsidiary"] || "unknown";
    const dir = path.join("uploads", "excel", subsidiary);

    console.log(`Excel upload destination directory: ${dir}`);

    try {
      ensureDir(dir);
      cb(null, dir);
    } catch (error) {
      console.error(`Failed to create upload directory: ${dir}`, error);
      // Fallback to a generic directory if specific one fails
      const fallbackDir = "uploads/excel";
      ensureDir(fallbackDir);
      cb(null, fallbackDir);
    }
  },
  filename: function (req, file, cb) {
    const timestamp = Date.now();
    const originalName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_'); // Sanitize filename
    const ext = path.extname(originalName) || '.xlsx';
    const newFilename = `revocations-${timestamp}${ext}`;

    console.log(`Generated filename for Excel upload: ${newFilename}`);
    cb(null, newFilename);
  },
});

// Excel file filter with better error handling
const excelFilter = (req, file, cb) => {
  console.log(`Validating file: ${file.originalname}, mimetype: ${file.mimetype}`);

  const allowed = [
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-excel",
    // Add more common Excel mimetypes
    "application/excel",
    "application/x-excel",
    "application/x-msexcel",
  ];

  // Also check file extension as a fallback
  const ext = path.extname(file.originalname).toLowerCase();
  const validExtensions = ['.xlsx', '.xls', '.csv'];

  if (allowed.includes(file.mimetype) || validExtensions.includes(ext)) {
    console.log(`File validation passed: ${file.originalname}`);
    cb(null, true);
  } else {
    console.error(`File validation failed: ${file.originalname}, mimetype: ${file.mimetype}`);
    cb(new Error(`Only Excel files are allowed (.xlsx, .xls). Received: ${file.mimetype}`), false);
  }
};

// ========== Exports ==========
// For signature uploads in single form
const uploadSignature = multer({
  storage: signatureStorage,
  fileFilter: imageFilter,
});

// For HR bulk uploads with better error handling
const uploadExcel = multer({
  storage: excelStorage,
  fileFilter: excelFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max file size
  }
}).single('file');

// Wrap the uploadExcel middleware to handle errors
const uploadExcelWithErrorHandling = (req, res, next) => {
  uploadExcel(req, res, function(err) {
    if (err) {
      console.error('Multer error during Excel upload:', err);
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          error: 'File too large. Maximum size is 10MB.'
        });
      }
      return res.status(400).json({
        error: err.message || 'Error uploading file'
      });
    }
    next();
  });
};

module.exports = {
  uploadSignature,
  uploadExcel: uploadExcelWithErrorHandling,
  rawUploadExcel: uploadExcel,
};
