/**
 * Role-based authorization middleware
 * Checks if the authenticated user has the required role(s) to access a route
 * 
 * @param {string|string[]} roles - Required role(s) to access the route
 * @returns {Function} Express middleware function
 */
const roleAuth = (roles) => {
  // Convert single role to array for consistent handling
  if (typeof roles === 'string') {
    roles = [roles];
  }

  return (req, res, next) => {
    // User should be attached to request by authJwt middleware
    if (!req.user) {
      return res.status(401).json({
        status: 'fail',
        message: 'Authentication required. Please log in.',
      });
    }

    // Check if user has one of the required roles
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        status: 'fail',
        message: 'You do not have permission to perform this action',
      });
    }

    // User has required role, proceed to next middleware
    next();
  };
};

module.exports = roleAuth;
