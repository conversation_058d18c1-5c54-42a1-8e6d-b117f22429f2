const extractMambuUser = require("../utils/extractMambuUser");

const auth = (req, res, next) => {
  let idToken = req.header("mambuUser");

  if (!idToken || idToken == "null") {
    console.error("no token found");
    return res.status(403).json({ error: "No token found,Unauthorized" });
  }

  const { userId, base_url, mambu_env, sub } = extractMambuUser(idToken);

  req.mambuUserID = userId;
  req.mambuData = {
    userId,
    base_url,
    mambu_env,
    sub,
  };
  return next();
};

module.exports = auth;
   