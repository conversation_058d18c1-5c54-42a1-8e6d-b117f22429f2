const jwt = require("jsonwebtoken");
const db = require("../db/models");
const User = db.User;

/**
 * JWT Authentication Middleware
 * Verifies the JWT token and attaches the user to the request object
 */
const authJwt = async (req, res, next) => {
  try {
    // Get token from header
    let token;
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }

    // Check if token exists
    if (!token) {
      return res.status(401).json({
        status: "fail",
        message: "You are not logged in. Please log in to get access.",
      });
    }

    // Verify token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "your-secret-key"
    );

    // Check if user still exists
    const currentUser = await User.findByPk(decoded.id);
    if (!currentUser) {
      return res.status(401).json({
        status: "fail",
        message: "The user belonging to this token no longer exists.",
      });
    }

    // Check if user is active
    if (currentUser.status === 'inactive') {
      return res.status(403).json({
        status: "fail",
        message: "Your account is inactive. Please contact an administrator.",
      });
    }

    // Remove password from user object
    const userWithoutPassword = { ...currentUser.get() };
    delete userWithoutPassword.password;

    // Grant access to protected route
    req.user = userWithoutPassword;
    next();
  } catch (err) {
    if (err.name === "JsonWebTokenError") {
      return res.status(401).json({
        status: "fail",
        message: "Invalid token. Please log in again.",
      });
    }

    if (err.name === "TokenExpiredError") {
      return res.status(401).json({
        status: "fail",
        message: "Your token has expired. Please log in again.",
      });
    }

    return res.status(401).json({
      status: "fail",
      message: "Authentication failed. Please log in again.",
    });
  }
};

module.exports = authJwt;
