/**
 * User Management Audit Data Middleware
 * Prepares audit data for user management actions
 * 
 * @param {string} action - The action being performed (e.g., 'user-create', 'user-update', 'user-delete')
 * @returns {Function} Express middleware function
 */
const userAuditData = (action) => {
  return (req, res, next) => {
    // Initialize audit data object if it doesn't exist
    if (!req.auditData) {
      req.auditData = {};
    }

    // Set basic audit data
    req.auditData.action = action;
    req.auditData.formType = 'user-management';
    
    // For user update/delete, the ID will be in the params
    if (req.params.id) {
      req.auditData.formId = req.params.id;
    }
    
    // Add request metadata
    req.auditData.ip = req.ip;
    req.auditData.path = req.path;
    req.auditData.method = req.method;
    req.auditData.agent = req.get('user-agent');
    
    // Add user info if available
    if (req.user) {
      req.auditData.createdBy = req.user.email;
      req.auditData.subId = req.user.subId;
    }
    
    next();
  };
};

module.exports = userAuditData;
