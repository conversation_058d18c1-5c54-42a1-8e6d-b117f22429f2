const populateAuditData = (req, res, next) => {
  if (!req.body) {
    console.error("Error: Request body is missing");
    return next();
  }

  // Combine firstName & lastName if available
  const fullName =
    `${req.body.firstName || ""} ${req.body.lastName || ""}`.trim();

  req.auditData = {
    userName: fullName,
    action: "Access Request",
    formType: req.body.formType || "access",
    formId: req.body.formId || Math.floor(Math.random() * 1000),
    systemName: req.body.systemName,
    branch: req.body.branch,
    role: req.body.role
  };

  console.log("Populated Audit Data:", req.auditData);
  next();
};

module.exports = populateAuditData;
