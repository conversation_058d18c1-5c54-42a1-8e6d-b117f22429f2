/**
 * Subsidiary-specific email configuration
 * This file contains email settings for each subsidiary
 * For use with external email service
 */

require('dotenv').config();

module.exports = {
  // Platinum Kenya
  platinumkenya: {
    itSenderEmail: process.env.PLATINUMKENYA_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.PLATINUMKENYA_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Platinum Kenya HR Portal',
    tenantId: 'platinumkenya'
  },

  // Premier Kenya
  premierkenya: {
    itSenderEmail: process.env.PREMIERKENYA_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.PREMIERKENYA_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.PREMIER_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Premier Kenya HR Portal',
    tenantId: 'premierkenya'
  },

  // Momentum Credit
  momentumcredit: {
    itSenderEmail: process.env.MOMENTUMCREDIT_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.MOMENTUMCREDIT_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.MOMENTUM_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Momentum Credit HR Portal',
    tenantId: 'momentumcredit'
  },

  // Platinum Tanzania
  platinumtanzania: {
    itSenderEmail: process.env.PLATINUMTANZANIA_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.PLATINUMTANZANIA_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.PLATINUMTZ_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Platinum Tanzania HR Portal',
    tenantId: 'platinumtanzania'
  },

  // Premier Fanikiwa
  premierfanikiwa: {
    itSenderEmail: process.env.PREMIERFANIKIWA_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.PREMIERFANIKIWA_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.FANIKIWA_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Premier Fanikiwa HR Portal',
    tenantId: 'premierfanikiwa'
  },

  // Platinum Uganda
  platinumuganda: {
    itSenderEmail: process.env.PLATINUMUGANDA_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.PLATINUMUGANDA_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.PLATINUMUG_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Platinum Uganda HR Portal',
    tenantId: 'platinumuganda'
  },

  // Premier Uganda
  premieruganda: {
    itSenderEmail: process.env.PREMIERUGANDA_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.PREMIERUGANDA_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.PREMIERUG_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Premier Uganda HR Portal',
    tenantId: 'premieruganda'
  },

  // Spectrum Zambia
  spectrumzambia: {
    itSenderEmail: process.env.SPECTRUMZAMBIA_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.SPECTRUMZAMBIA_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.SPECTRUM_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Spectrum Zambia HR Portal',
    tenantId: 'spectrumzambia'
  },

  // Premier South Africa
  premiersouthafrica: {
    itSenderEmail: process.env.PREMIERSOUTHAFRICA_IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.PREMIERSOUTHAFRICA_IT_EMAIL_PASS || process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.PREMIERSA_IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'Premier South Africa HR Portal',
    tenantId: 'premiersouthafrica'
  },

  // Default fallback configuration (uses Platinum Kenya settings)
  default: {
    itSenderEmail: process.env.IT_EMAIL_USER || '<EMAIL>',
    itSenderPass: process.env.IT_EMAIL_PASS,
    itReceiverEmail: process.env.IT_EMAIL_RECEIVER || '<EMAIL>',
    senderName: 'HR Portal',
    tenantId: 'platinumkenya'
  }
};

