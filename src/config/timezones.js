/**
 * Timezone configuration for each subsidiary
 * This ensures dates and times are displayed correctly for each region
 */

module.exports = {
  // Kenya subsidiaries (EAT - East Africa Time, UTC+3)
  platinumkenya: {
    timezone: 'Africa/Nairobi',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },
  premierkenya: {
    timezone: 'Africa/Nairobi',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },
  momentumcredit: {
    timezone: 'Africa/Nairobi',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },

  // Tanzania subsidiaries (EAT - East Africa Time, UTC+3)
  platinumtanzania: {
    timezone: 'Africa/Dar_es_Salaam',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },
  premierfanikiwa: {
    timezone: 'Africa/Dar_es_Salaam',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },

  // Uganda subsidiaries (EAT - East Africa Time, UTC+3)
  platinumuganda: {
    timezone: 'Africa/Kampala',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },
  premieruganda: {
    timezone: 'Africa/Kampala',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  },

  // Zambia subsidiary (CAT - Central Africa Time, UTC+2)
  spectrumzambia: {
    timezone: 'Africa/Lusaka',
    utcOffset: '+02:00',
    name: 'Central Africa Time'
  },

  // South Africa subsidiary (SAST - South Africa Standard Time, UTC+2)
  premiersouthafrica: {
    timezone: 'Africa/Johannesburg',
    utcOffset: '+02:00',
    name: 'South Africa Standard Time'
  },

  // Default fallback (Kenya time)
  default: {
    timezone: 'Africa/Nairobi',
    utcOffset: '+03:00',
    name: 'East Africa Time'
  }
};
