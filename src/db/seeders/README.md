# Database Seeders

This directory contains database seeders for the Digital User Access System.

## Available Seeders

1. `20240501000000-tenant-seed.js` - Creates all subsidiary tenants if they don't exist
2. `20240501000001-platinumkenya-users.js` - Creates sample users for the platinumkenya tenant
3. `20240501000002-premierkenya-users.js` - Creates sample users for the premierkenya tenant
4. `20240501000003-momentumcredit-users.js` - Creates sample users for the momentumcredit tenant
5. `20240501000004-platinumtanzania-users.js` - Creates sample users for the platinumtanzania tenant
6. `20240501000005-premierfanikiwa-users.js` - Creates sample users for the premierfanikiwa tenant
7. `20240501000006-platinumuganda-users.js` - Creates sample users for the platinumuganda tenant
8. `20240501000007-spectrumzambia-users.js` - Creates sample users for the spectrumzambia tenant
9. `20240501000008-premiersouthafrica-users.js` - Creates sample users for the premiersouthafrica tenant

## Running the Seeders

To run all seeders, use the following command:

```bash
npx sequelize-cli db:seed:all
```

Or use the npm script:

```bash
npm run seed
```

To run a specific seeder, use:

```bash
npx sequelize-cli db:seed --seed 20240501000000-tenant-seed.js
```

Or use the npm scripts for specific subsidiaries:

```bash
npm run seed:tenant
npm run seed:users:platinumkenya
npm run seed:users:premierkenya
npm run seed:users:momentumcredit
npm run seed:users:platinumtanzania
npm run seed:users:premierfanikiwa
npm run seed:users:platinumuganda
npm run seed:users:spectrumzambia
npm run seed:users:premiersouthafrica
```

## Undoing Seeders

To undo all seeders:

```bash
npx sequelize-cli db:seed:undo:all
```

To undo a specific seeder:

```bash
npx sequelize-cli db:seed:undo --seed 20240501000001-platinumkenya-users.js
```

## Sample Users for Each Subsidiary

Each subsidiary has the following sample users with the same pattern of credentials:

| Username   | Email                                  | Password        | Role        |
|------------|----------------------------------------|----------------|-------------|
| admin      | admin@[subsidiary-domain]              | Admin@123      | systemAdmin |
| hr         | hr@[subsidiary-domain]                 | Hr@123         | hr          |
| it         | it@[subsidiary-domain]                 | It@123         | it          |
| supervisor | supervisor@[subsidiary-domain]         | Supervisor@123 | supervisor  |

### Email Domains by Subsidiary

| Subsidiary          | Email Domain                |
|---------------------|----------------------------|
| platinumkenya       | platinumcredit.co.ke       |
| premiergroup        | premiergroup.co.ke         |
| momentumcredit      | momentumcredit.co.ke       |
| platinumtanzania    | platinumcredit.co.tz       |
| premierfanikiwa     | premierfanikiwa.co.ke      |
| platinumuganda      | platinumcredit.co.ug       |
| spectrumzambia      | spectrum.co.zm             |
| premiersouthafrica  | premiercredit.co.za        |

These users can be used for testing and development purposes.
