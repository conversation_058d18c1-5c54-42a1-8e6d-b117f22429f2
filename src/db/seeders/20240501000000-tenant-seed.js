'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if platinumkenya tenant already exists
    const existingTenants = await queryInterface.sequelize.query(
      `SELECT * FROM tenant WHERE sub = 'platinumkenya'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (existingTenants.length === 0) {
      // Insert platinumkenya tenant if it doesn't exist
      await queryInterface.bulkInsert('tenant', [
        {
          sub: 'platinumkenya',
          url: 'https://platinumcredit.co.ke',
          matcher: 'platinumcredit.co.ke',
          name: 'Platinum Kenya Credit IT Department',
          uniqueId: 'platke-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'premierkenya',
          url: 'https://premiergroup.co.ke',
          matcher: 'premiergroup.co.ke',
          name: 'Premier Kenya IT Department',
          uniqueId: 'premierke-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'premiergroup',
          url: 'https://premiergroup.co.ke',
          matcher: 'premiergroup.co.ke',
          name: 'Premier Group IT Department',
          uniqueId: 'premiergroup-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'momentumcredit',
          url: 'https://momentumcredit.co.ke',
          matcher: 'momentumcredit.co.ke',
          name: 'Momentum Credit IT Department',
          uniqueId: 'momentumke-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'platinumtanzania',
          url: 'https://platinumcredit.co.tz',
          matcher: 'platinumcredit.co.tz',
          name: 'Platinum Tanzania Credit IT Department',
          uniqueId: 'platz-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'premierfanikiwa',
          url: 'https://premierfanikiwa.co.ke',
          matcher: 'premierfanikiwa.co.ke',
          name: 'Premier Fanikiwa IT Department',
          uniqueId: 'premierfanikiwa-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'platinumuganda',
          url: 'https://platinumcredit.co.ug',
          matcher: 'platinumcredit.co.ug',
          name: 'Platinum Uganda IT Department',
          uniqueId: 'platug-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'premieruganda',
          url: 'https://premiercredit.co.ug',
          matcher: 'premiercredit.co.ug',
          name: 'Premier Uganda IT Department',
          uniqueId: 'premierug-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'spectrumzambia',
          url: 'https://spectrum.co.zm',
          matcher: 'spectrum.co.zm',
          name: 'Spectrum Zambia IT Department',
          uniqueId: 'spectrumzm-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          sub: 'premiersouthafrica',
          url: 'https://premiercredit.co.za',
          matcher: 'premiercredit.co.za',
          name: 'PREMIER CREDIT (PTY) LTD IT Department',
          uniqueId: 'premierza-' + Date.now(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);
      console.log('Platinum Kenya tenant created successfully');
    } else {
      console.log('Platinum Kenya tenant already exists, skipping creation');
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove all tenants
    await queryInterface.bulkDelete('tenant', {
      sub: {
        [Sequelize.Op.in]: [
          'platinumkenya',
          'premierkenya',
          'premiergroup',
          'momentumcredit',
          'platinumtanzania',
          'premierfanikiwa',
          'platinumuganda',
          'premieruganda',
          'spectrumzambia',
          'premiersouthafrica'
        ]
      }
    });
  }
};
