'use strict';
const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get the platinumkenya tenant ID
    const tenants = await queryInterface.sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'platinumkenya'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tenants.length === 0) {
      console.log('Platinum Kenya tenant not found. Please run the tenant seeder first.');
      return;
    }

    const tenantId = tenants[0].id;

    // Hash passwords
    const salt = bcrypt.genSaltSync(10);
    const hashPassword = (password) => bcrypt.hashSync(password, salt);

    // Sample users for platinumkenya
    const users = [
      {
        username: 'chuu',
        email: '<EMAIL>',
        password: hashPassword('chuu@12345'),
        sub: 'premiergroup',
        subId: tenantId.toString(),
        employerId: 'EMP0010',
        createdat: new Date(),
        updatedat: new Date()
      },
      {
        username: 'Adip<PERSON>',
        email: '<EMAIL>',
        password: hashPassword('Admin@123'),
        sub: 'platinumkenya',
        subId: tenantId.toString(),
        employerId: 'EMP001',
        createdat: new Date(),
        updatedat: new Date()
      },
      {
        username: 'manager',
        email: '<EMAIL>',
        password: hashPassword('Manager@123'),
        sub: 'platinumkenya',
        subId: tenantId.toString(),
        employerId: 'EMP002',
        createdat: new Date(),
        updatedat: new Date()
      },
      {
        username: 'user',
        email: '<EMAIL>',
        password: hashPassword('User@123'),
        sub: 'platinumkenya',
        subId: tenantId.toString(),
        employerId: 'EMP003',
        createdat: new Date(),
        updatedat: new Date()
      },
      {
        username: 'hr',
        email: '<EMAIL>',
        password: hashPassword('Hr@123'),
        sub: 'platinumkenya',
        subId: tenantId.toString(),
        employerId: 'EMP004',
        createdat: new Date(),
        updatedat: new Date()
      },
      {
        username: 'it',
        email: '<EMAIL>',
        password: hashPassword('It@123'),
        sub: 'platinumkenya',
        subId: tenantId.toString(),
        employerId: 'EMP005',
        createdat: new Date(),
        updatedat: new Date()
      }
    ];

    // Check for existing users to avoid duplicates
    for (const user of users) {
      const existingUser = await queryInterface.sequelize.query(
        `SELECT * FROM "user" WHERE email = '${user.email}'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingUser.length === 0) {
        await queryInterface.bulkInsert('user', [user]);
        console.log(`User ${user.username} created successfully`);
      } else {
        console.log(`User ${user.username} already exists, skipping creation`);
      }
    }
  },

  async down(queryInterface, Sequelize) {
    // Get the platinumkenya tenant ID
    const tenants = await queryInterface.sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'platinumkenya'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (tenants.length === 0) {
      return;
    }

    const tenantId = tenants[0].id;

    // Delete the users for platinumkenya
    await queryInterface.bulkDelete('user', {
      subId: tenantId.toString()
    });
  }
};
