'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Add new fields for Premier Uganda to UserAccessRequests table
      await queryInterface.addColumn('UserAccessRequests', 'middleName', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'nin', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'dateOfBirth', {
        type: Sequelize.DATEONLY,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'nextOfKinName', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'nextOfKinMobile', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'nextOfKinRelationship', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'nextOfKinDependents', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'tin', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'staffId', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'position', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('UserAccessRequests', 'startDate', {
        type: Sequelize.DATEONLY,
        allowNull: true,
      });

      // Make email field nullable for Premier Uganda (since it's optional)
      await queryInterface.changeColumn('UserAccessRequests', 'email', {
        type: Sequelize.STRING,
        allowNull: true,
        validate: {
          isEmail: true,
        },
      });

      console.log('Premier Uganda fields added to UserAccessRequests table successfully.');
    } catch (error) {
      console.error('Error adding Premier Uganda fields:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Remove the added columns
      await queryInterface.removeColumn('UserAccessRequests', 'middleName');
      await queryInterface.removeColumn('UserAccessRequests', 'nin');
      await queryInterface.removeColumn('UserAccessRequests', 'dateOfBirth');
      await queryInterface.removeColumn('UserAccessRequests', 'nextOfKinName');
      await queryInterface.removeColumn('UserAccessRequests', 'nextOfKinMobile');
      await queryInterface.removeColumn('UserAccessRequests', 'nextOfKinRelationship');
      await queryInterface.removeColumn('UserAccessRequests', 'nextOfKinDependents');
      await queryInterface.removeColumn('UserAccessRequests', 'tin');
      await queryInterface.removeColumn('UserAccessRequests', 'staffId');
      await queryInterface.removeColumn('UserAccessRequests', 'position');
      await queryInterface.removeColumn('UserAccessRequests', 'startDate');

      // Revert email field to not nullable
      await queryInterface.changeColumn('UserAccessRequests', 'email', {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          isEmail: true,
        },
      });

      console.log('Premier Uganda fields removed from UserAccessRequests table successfully.');
    } catch (error) {
      console.error('Error removing Premier Uganda fields:', error);
      throw error;
    }
  }
};
