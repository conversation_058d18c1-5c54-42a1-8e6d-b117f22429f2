'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the table already exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'UserAccessRequests'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (tableExists[0].exists) {
        console.log('Table UserAccessRequests already exists. Skipping creation.');
        return;
      }

      // Create the table
      await queryInterface.createTable('UserAccessRequests', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        subId: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        systemName: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        branch: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        firstName: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        lastName: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        email: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        telephone: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        department: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        accessType: {
          type: Sequelize.ARRAY(Sequelize.STRING),
          allowNull: false,
        },
        role: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        previousRole: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        reason: {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        attachments: {
          type: Sequelize.JSONB,
          allowNull: true,
        },
        approvalStatus: {
          type: Sequelize.STRING,
          defaultValue: "Pending",
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
      });

      console.log('Table UserAccessRequests created successfully.');
      
      // Add some sample data for testing
      await queryInterface.bulkInsert('UserAccessRequests', [
        {
          subId: 'platinumkenya',
          systemName: 'Mambu',
          branch: 'Nairobi',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          telephone: '+254712345678',
          department: 'IT',
          accessType: ['Read', 'Write'],
          role: 'Admin',
          previousRole: 'User',
          reason: 'Promotion',
          approvalStatus: 'Pending',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          subId: 'platinumkenya',
          systemName: 'CRM',
          branch: 'Mombasa',
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          telephone: '+254723456789',
          department: 'HR',
          accessType: ['Read'],
          role: 'User',
          previousRole: null,
          reason: 'New hire',
          approvalStatus: 'Approved by HR',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          subId: 'platinumuganda',
          systemName: 'Finance',
          branch: 'Kampala',
          firstName: 'Robert',
          lastName: 'Johnson',
          email: '<EMAIL>',
          telephone: '+256712345678',
          department: 'Finance',
          accessType: ['Read', 'Write', 'Delete'],
          role: 'Manager',
          previousRole: 'Supervisor',
          reason: 'Promotion',
          approvalStatus: 'Approved by IT',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);

      console.log('Sample data inserted into UserAccessRequests table.');
    } catch (error) {
      console.error('Error creating UserAccessRequests table:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable('UserAccessRequests');
      console.log('Table UserAccessRequests dropped successfully.');
    } catch (error) {
      console.error('Error dropping UserAccessRequests table:', error);
      throw error;
    }
  }
};
