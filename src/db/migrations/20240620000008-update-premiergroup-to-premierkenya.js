'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      console.log('Starting migration to update premiergroup to premierkenya');
      
      // 1. First, check if premiergroup tenant exists
      const premiergroupTenant = await queryInterface.sequelize.query(
        `SELECT id, sub, name FROM tenant WHERE sub = 'premiergroup'`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (premiergroupTenant.length > 0) {
        console.log('Found premiergroup tenant:', premiergroupTenant[0]);
        
        // 2. Check if premierkenya tenant already exists
        const premierkenyaTenant = await queryInterface.sequelize.query(
          `SELECT id, sub, name FROM tenant WHERE sub = 'premierkenya'`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );
        
        if (premierkenyaTenant.length > 0) {
          console.log('premierkenya tenant already exists:', premierkenyaTenant[0]);
          
          // 3. Update users from premiergroup to premierkenya
          await queryInterface.sequelize.query(
            `UPDATE "user" SET sub = 'premierkenya', subId = '${premierkenyaTenant[0].id}' WHERE sub = 'premiergroup'`
          );
          console.log('Updated users from premiergroup to premierkenya');
          
          // 4. Delete the premiergroup tenant
          await queryInterface.sequelize.query(
            `DELETE FROM tenant WHERE sub = 'premiergroup'`
          );
          console.log('Deleted premiergroup tenant');
        } else {
          // If premierkenya doesn't exist, rename premiergroup to premierkenya
          await queryInterface.sequelize.query(
            `UPDATE tenant SET sub = 'premierkenya', name = 'Premier Kenya IT Department' WHERE sub = 'premiergroup'`
          );
          console.log('Renamed premiergroup tenant to premierkenya');
          
          // Update all users with the premiergroup subsidiary
          await queryInterface.sequelize.query(
            `UPDATE "user" SET sub = 'premierkenya' WHERE sub = 'premiergroup'`
          );
          console.log('Updated users from premiergroup to premierkenya');
        }
      } else {
        console.log('premiergroup tenant not found, checking if premierkenya exists');
        
        // Check if premierkenya tenant exists
        const premierkenyaTenant = await queryInterface.sequelize.query(
          `SELECT id, sub, name FROM tenant WHERE sub = 'premierkenya'`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );
        
        if (premierkenyaTenant.length === 0) {
          console.log('Neither premiergroup nor premierkenya tenant exists, creating premierkenya tenant');
          
          // Create premierkenya tenant
          await queryInterface.sequelize.query(`
            INSERT INTO tenant (sub, url, matcher, name, uniqueId, "createdAt", "updatedAt")
            VALUES (
              'premierkenya',
              'https://premiergroup.co.ke',
              'premiergroup.co.ke',
              'Premier Kenya IT Department',
              'premierke-${Date.now()}',
              NOW(),
              NOW()
            )
          `);
          console.log('Created premierkenya tenant');
        } else {
          console.log('premierkenya tenant already exists:', premierkenyaTenant[0]);
        }
      }
      
      console.log('Migration completed successfully');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      console.log('Starting rollback migration to revert premierkenya to premiergroup');
      
      // 1. Check if premierkenya tenant exists
      const premierkenyaTenant = await queryInterface.sequelize.query(
        `SELECT id, sub, name FROM tenant WHERE sub = 'premierkenya'`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (premierkenyaTenant.length > 0) {
        console.log('Found premierkenya tenant:', premierkenyaTenant[0]);
        
        // 2. Check if premiergroup tenant already exists
        const premiergroupTenant = await queryInterface.sequelize.query(
          `SELECT id, sub, name FROM tenant WHERE sub = 'premiergroup'`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );
        
        if (premiergroupTenant.length > 0) {
          console.log('premiergroup tenant already exists:', premiergroupTenant[0]);
          
          // 3. Update users from premierkenya to premiergroup
          await queryInterface.sequelize.query(
            `UPDATE "user" SET sub = 'premiergroup', subId = '${premiergroupTenant[0].id}' WHERE sub = 'premierkenya'`
          );
          console.log('Updated users from premierkenya to premiergroup');
          
          // 4. Delete the premierkenya tenant
          await queryInterface.sequelize.query(
            `DELETE FROM tenant WHERE sub = 'premierkenya'`
          );
          console.log('Deleted premierkenya tenant');
        } else {
          // If premiergroup doesn't exist, rename premierkenya to premiergroup
          await queryInterface.sequelize.query(
            `UPDATE tenant SET sub = 'premiergroup', name = 'Premier Credit IT Department' WHERE sub = 'premierkenya'`
          );
          console.log('Renamed premierkenya tenant to premiergroup');
          
          // Update all users with the premierkenya subsidiary
          await queryInterface.sequelize.query(
            `UPDATE "user" SET sub = 'premiergroup' WHERE sub = 'premierkenya'`
          );
          console.log('Updated users from premierkenya to premiergroup');
        }
      } else {
        console.log('premierkenya tenant not found, no rollback needed');
      }
      
      console.log('Rollback migration completed successfully');
    } catch (error) {
      console.error('Rollback migration failed:', error);
      throw error;
    }
  }
};
