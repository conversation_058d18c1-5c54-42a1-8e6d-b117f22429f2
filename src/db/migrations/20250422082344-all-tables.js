'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('tenant', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      sub: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      url: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      matcher: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      uniqueId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    })

    await queryInterface.createTable('user', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      username: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true,
        },
      },
      password: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      sub: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      subId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      employerId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    })

    await queryInterface.createTable('UserAccessRequests', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      subId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      systemName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      branch: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      firstName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      lastName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      telephone: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      department: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      accessType: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: false,
      },
      role: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      previousRole: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      reason: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      attachments: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      approvalStatus: {
        type: Sequelize.STRING,
        defaultValue: "Pending",
      },
    })

    await queryInterface.createTable('approvals', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      formId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      formType: {
        type: Sequelize.ENUM('access', 'revocation'),
        allowNull: false,
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('Pending', 'Approved', 'Rejected'),
        defaultValue: 'Pending',
      },
      comments: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      subId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
    })

    await queryInterface.createTable('Revocations', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      fullName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      employeeId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      jobTitle: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      department: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      contact: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      systems: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        allowNull: false,
      },
      reason: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      otherReason: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      approverName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      approverJobTitle: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      approverContact: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      signaturePath: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      submittedBy: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      subsidiary: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM('Pending', 'Completed', 'Rejected', 'Approved'),
        defaultValue: 'Pending',
      },
      actionedByIT: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      actionedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      rejectionReason: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      lastWorkingDay: {
        type: Sequelize.DATEONLY,
        allowNull: true,
      },
    })

    await queryInterface.createTable("audit_logs", {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      userName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      action: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      formType: {
        type: Sequelize.ENUM('access', 'revocation', 'approval'),
        allowNull: false,
      },
      formId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      subId: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      systemName: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      branch: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      role: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      approvers: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      ip: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      path: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      method: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      agent: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      timestamp: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
      },
    },)
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */

    await queryInterface.dropTable('Revocations');
    await queryInterface.dropTable('audit_logs');
    await queryInterface.dropTable('approvals');
    await queryInterface.dropTable('UserAccessRequests');
    await queryInterface.dropTable('user');
    await queryInterface.dropTable('tenant');
  }
};
