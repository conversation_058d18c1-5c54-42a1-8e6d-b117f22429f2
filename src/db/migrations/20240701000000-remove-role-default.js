'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }

      // Check if the role column has a default value
      const columnInfo = await queryInterface.sequelize.query(
        `SELECT column_default 
         FROM information_schema.columns 
         WHERE table_name = 'user' AND column_name = 'role';`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      console.log('Current role column default:', columnInfo[0]?.column_default);

      // Remove the default value from the role column
      await queryInterface.sequelize.query(`
        ALTER TABLE "user" 
        ALTER COLUMN "role" DROP DEFAULT;
      `);

      console.log('Successfully removed default value from role column');

      // Update any systemAdmin roles that should be something else
      // This is a temporary fix to correct existing data
      await queryInterface.sequelize.query(`
        UPDATE "user"
        SET role = 'supervisor'
        WHERE email = '<EMAIL>';
      `);

      console.log('Successfully updated specific user roles');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Restore the default value for the role column
      await queryInterface.sequelize.query(`
        ALTER TABLE "user" 
        ALTER COLUMN "role" SET DEFAULT 'supervisor'::enum_user_role;
      `);

      console.log('Successfully restored default value for role column');
    } catch (error) {
      console.error('Migration rollback error:', error);
      throw error;
    }
  }
};
