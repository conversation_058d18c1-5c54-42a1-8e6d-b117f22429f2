'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }

      // Get the current subid column info
      const columnInfo = await queryInterface.sequelize.query(
        `SELECT column_name, data_type, udt_name 
         FROM information_schema.columns 
         WHERE table_name = 'user' AND column_name = 'subid';`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (columnInfo.length === 0) {
        console.log('subid column does not exist. Skipping migration.');
        return;
      }

      console.log('Current subid column info:', columnInfo[0]);

      // Check if the column is already an integer
      if (columnInfo[0].data_type === 'integer') {
        console.log('subid column is already an integer. Skipping migration.');
        return;
      }

      // Get all tenants
      const tenants = await queryInterface.sequelize.query(
        `SELECT id, sub FROM tenant;`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      console.log('Found tenants:', tenants);

      // Create a map of subsidiary names to tenant IDs
      const subToIdMap = {};
      tenants.forEach(tenant => {
        subToIdMap[tenant.sub] = tenant.id;
      });

      console.log('Subsidiary to ID map:', subToIdMap);

      // Create a temporary column to store the integer values
      console.log('Creating temporary column subid_int...');
      await queryInterface.sequelize.query(`
        ALTER TABLE "user" ADD COLUMN subid_int INTEGER;
      `);

      // Update the temporary column with integer values from subid
      console.log('Updating temporary column with integer values...');
      
      // For each tenant, update the users with the correct tenant ID
      for (const [sub, id] of Object.entries(subToIdMap)) {
        console.log(`Updating users for subsidiary ${sub} with tenant ID ${id}...`);
        
        await queryInterface.sequelize.query(`
          UPDATE "user" 
          SET subid_int = ${id}
          WHERE sub = '${sub}';
        `);
      }

      // Drop the old subid column
      console.log('Dropping old subid column...');
      await queryInterface.sequelize.query(`
        ALTER TABLE "user" DROP COLUMN subid;
      `);

      // Rename the temporary column to subid
      console.log('Renaming temporary column to subid...');
      await queryInterface.sequelize.query(`
        ALTER TABLE "user" RENAME COLUMN subid_int TO subid;
      `);

      console.log('Successfully fixed subid column');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // This migration is not reversible
    console.log('This migration is not reversible');
  }
};
