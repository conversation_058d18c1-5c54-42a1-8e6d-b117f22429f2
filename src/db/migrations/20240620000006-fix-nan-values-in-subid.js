'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      console.log('Starting migration to fix NaN values in subid column...');
      
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }

      // Get the current subid column info
      const columnInfo = await queryInterface.sequelize.query(
        `SELECT column_name, data_type, udt_name 
         FROM information_schema.columns 
         WHERE table_name = 'user' AND column_name = 'subid';`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (columnInfo.length === 0) {
        console.log('subid column does not exist. Skipping migration.');
        return;
      }

      console.log('Current subid column info:', columnInfo[0]);

      // Get all tenants
      const tenants = await queryInterface.sequelize.query(
        `SELECT id, sub FROM tenant;`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      console.log('Found tenants:', tenants);

      // Create a map of subsidiary names to tenant IDs
      const subToIdMap = {};
      tenants.forEach(tenant => {
        subToIdMap[tenant.sub] = tenant.id;
      });

      console.log('Subsidiary to ID map:', subToIdMap);

      // First, let's check if there are any users with NaN values
      const usersWithNaN = await queryInterface.sequelize.query(
        `SELECT id, email, sub FROM "user" WHERE subid::text = 'NaN' OR subid IS NULL;`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      console.log(`Found ${usersWithNaN.length} users with NaN or NULL subid values`);

      // Update users with NaN values
      for (const user of usersWithNaN) {
        const correctTenantId = subToIdMap[user.sub];
        
        if (!correctTenantId) {
          console.log(`Warning: No tenant found for user ${user.email} with sub ${user.sub}`);
          continue;
        }

        console.log(`Updating user ${user.email} with correct tenant ID: ${correctTenantId}`);
        
        await queryInterface.sequelize.query(`
          UPDATE "user" 
          SET subid = ${correctTenantId} 
          WHERE id = ${user.id}
        `);
      }

      // Now let's check if there are any users with string values in subid
      const usersWithStringSubId = await queryInterface.sequelize.query(
        `SELECT id, email, sub, subid FROM "user" 
         WHERE subid IS NOT NULL AND subid::text != 'NaN' AND subid::text ~ '[^0-9]';`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      console.log(`Found ${usersWithStringSubId.length} users with string values in subid`);

      // Update users with string values
      for (const user of usersWithStringSubId) {
        const correctTenantId = subToIdMap[user.sub];
        
        if (!correctTenantId) {
          console.log(`Warning: No tenant found for user ${user.email} with sub ${user.sub}`);
          continue;
        }

        console.log(`Updating user ${user.email} with correct tenant ID: ${correctTenantId} (was: ${user.subid})`);
        
        await queryInterface.sequelize.query(`
          UPDATE "user" 
          SET subid = ${correctTenantId} 
          WHERE id = ${user.id}
        `);
      }

      // Finally, let's ensure the subid column is an integer
      console.log('Ensuring subid column is an integer...');
      
      // Check if we need to alter the column type
      if (columnInfo[0].data_type !== 'integer') {
        console.log('Converting subid column to integer...');
        
        // Create a temporary column
        await queryInterface.sequelize.query(`
          ALTER TABLE "user" ADD COLUMN subid_temp INTEGER;
        `);
        
        // Copy data to the temporary column, converting to integers
        await queryInterface.sequelize.query(`
          UPDATE "user" SET subid_temp = subid::integer WHERE subid IS NOT NULL AND subid::text ~ '^[0-9]+$';
        `);
        
        // Drop the original column
        await queryInterface.sequelize.query(`
          ALTER TABLE "user" DROP COLUMN subid;
        `);
        
        // Rename the temporary column
        await queryInterface.sequelize.query(`
          ALTER TABLE "user" RENAME COLUMN subid_temp TO subid;
        `);
        
        console.log('Successfully converted subid column to integer');
      } else {
        console.log('subid column is already an integer, no conversion needed');
      }

      console.log('Migration completed successfully');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // This migration is not reversible
    console.log('This migration is not reversible');
  }
};
