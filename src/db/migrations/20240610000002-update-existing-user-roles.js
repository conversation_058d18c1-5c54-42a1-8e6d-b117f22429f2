'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }
      
      // Update any existing users with invalid roles to 'admin'
      await queryInterface.sequelize.query(`
        UPDATE "user"
        SET role = 'admin'
        WHERE role NOT IN ('admin', 'it')
      `);
      
      console.log('Successfully updated existing user roles');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // This migration cannot be undone as we don't know the original values
    console.log('This migration cannot be undone as we don\'t know the original role values.');
  }
};
