'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the role column already exists
      const tableInfo = await queryInterface.describeTable('user');

      if (!tableInfo.role) {
        console.log('Adding role column to user table...');
        // Add role column to user table
        await queryInterface.addColumn('user', 'role', {
          type: Sequelize.ENUM('admin', 'it'),
          allowNull: false,
          defaultValue: 'admin',
        });
        console.log('Role column added successfully.');
      } else {
        console.log('Role column already exists, skipping migration.');
      }
    } catch (error) {
      console.error('Migration error:', error.message);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Check if the role column exists before trying to remove it
      const tableInfo = await queryInterface.describeTable('user');

      if (tableInfo.role) {
        console.log('Removing role column from user table...');
        // Remove role column from user table
        await queryInterface.removeColumn('user', 'role');
        console.log('Role column removed successfully.');

        // Remove the ENUM type
        await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_user_role";');
      } else {
        console.log('Role column does not exist, skipping rollback.');
      }
    } catch (error) {
      console.error('Migration rollback error:', error.message);
      throw error;
    }
  }
};
