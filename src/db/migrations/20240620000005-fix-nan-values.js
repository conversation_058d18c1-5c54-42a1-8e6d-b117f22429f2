'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }

      // Get all tenants
      const tenants = await queryInterface.sequelize.query(
        `SELECT id, sub FROM tenant;`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      console.log('Found tenants:', tenants);

      // Create a map of subsidiary names to tenant IDs
      const subToIdMap = {};
      tenants.forEach(tenant => {
        subToIdMap[tenant.sub] = tenant.id;
      });

      console.log('Subsidiary to ID map:', subToIdMap);

      // Find users with NaN or invalid subId values
      const usersWithInvalidSubId = await queryInterface.sequelize.query(
        `SELECT id, email, sub, subid FROM "user" WHERE subid IS NULL OR subid::text = 'NaN';`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      console.log('Found users with invalid subId:', usersWithInvalidSubId);

      // Update each user with the correct tenant ID based on their subsidiary
      for (const user of usersWithInvalidSubId) {
        const correctTenantId = subToIdMap[user.sub];
        
        if (!correctTenantId) {
          console.log(`Warning: No tenant found for user ${user.email} with sub ${user.sub}`);
          continue;
        }

        console.log(`Updating user ${user.email} (${user.id}): changing subId from ${user.subid} to ${correctTenantId}`);
        
        await queryInterface.sequelize.query(`
          UPDATE "user" 
          SET subid = ${correctTenantId} 
          WHERE id = ${user.id}
        `);
      }

      console.log('Successfully fixed NaN values in subId column');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // This migration is not reversible
    console.log('This migration is not reversible');
  }
};
