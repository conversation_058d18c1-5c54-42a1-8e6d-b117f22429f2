'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }

      // Get the current ENUM values for role
      const enumQuery = `
        SELECT e.enumlabel
        FROM pg_enum e
        JOIN pg_type t ON e.enumtypid = t.oid
        WHERE t.typname = 'enum_user_role'
      `;
      const [enumResults] = await queryInterface.sequelize.query(enumQuery);
      const currentEnumValues = enumResults.map(r => r.enumlabel);

      console.log('Current ENUM values:', currentEnumValues);

      // Check if we need to add new ENUM values
      const newEnumValues = ['systemAdmin', 'hr', 'it', 'supervisor'];
      const valuesToAdd = newEnumValues.filter(v => !currentEnumValues.includes(v));

      if (valuesToAdd.length > 0) {
        console.log('Adding new ENUM values:', valuesToAdd);

        // Add new values to the ENUM type
        for (const value of valuesToAdd) {
          try {
            await queryInterface.sequelize.query(`
              ALTER TYPE "enum_user_role" ADD VALUE IF NOT EXISTS '${value}';
            `);
            console.log(`Added '${value}' to enum_user_role`);
          } catch (err) {
            console.log(`Error adding '${value}' to enum: ${err.message}`);
          }
        }
      }

      // Check if role_new column exists and drop it if it does
      try {
        const columnExists = await queryInterface.sequelize.query(
          `SELECT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_name = 'user' AND column_name = 'role_new'
          );`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );

        if (columnExists[0].exists) {
          console.log('Dropping role_new column...');
          await queryInterface.sequelize.query(`
            ALTER TABLE "user" DROP COLUMN IF EXISTS role_new;
          `);
          console.log('Dropped role_new column');
        }
      } catch (error) {
        console.error('Error dropping role_new column:', error.message);
      }

      // Add status column if it doesn't exist
      try {
        const statusColumnExists = await queryInterface.sequelize.query(
          `SELECT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_name = 'user' AND column_name = 'status'
          );`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );

        if (!statusColumnExists[0].exists) {
          console.log('Adding status column...');
          await queryInterface.addColumn('user', 'status', {
            type: Sequelize.ENUM('active', 'inactive'),
            defaultValue: 'active',
            allowNull: false
          });
          console.log('Added status column');
        }
      } catch (err) {
        console.log('Status column may already exist:', err.message);
      }

      // Set status to 'active' for all users if it's not already set
      await queryInterface.sequelize.query(`
        UPDATE "user"
        SET status = 'active'
        WHERE status IS NULL
      `);

      console.log('Successfully updated user roles and status');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // This migration is not reversible
    console.log('This migration is not reversible');
  }
};
