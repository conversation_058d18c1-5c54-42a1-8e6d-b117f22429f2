'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the table exists first
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'audit_logs'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (!tableExists[0].exists) {
        console.log('Table audit_logs does not exist yet. Skipping migration.');
        return;
      }

      // Check if the enum type exists
      const enumExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM pg_type
          WHERE typname = 'enum_audit_logs_formtype'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (!enumExists[0].exists) {
        console.log('ENUM type enum_audit_logs_formtype does not exist. Creating it with all values.');
        // Create a new column with the updated enum type
        await queryInterface.changeColumn('audit_logs', 'formType', {
          type: Sequelize.ENUM('access', 'revocation', 'approval', 'user-management'),
          allowNull: false
        });
      } else {
        // The enum exists, try to add the new value
        try {
          await queryInterface.sequelize.query(`
            ALTER TYPE "enum_audit_logs_formtype" ADD VALUE IF NOT EXISTS 'user-management';
          `);
          console.log('Added user-management to enum_audit_logs_formtype');
        } catch (error) {
          console.log('Error adding value to ENUM, may already exist:', error.message);
        }
      }
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Unfortunately, PostgreSQL doesn't support removing values from an ENUM type
    // The only way would be to create a new type, update the column, and drop the old type
    // This is complex and potentially risky, so we'll leave it as is
    console.log('Cannot remove ENUM value in down migration. Values will remain.');
  }
};
