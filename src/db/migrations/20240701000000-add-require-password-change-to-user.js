'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }

      // Check if the requirepasswordchange column already exists (lowercase to match database convention)
      const tableInfo = await queryInterface.describeTable('user');

      if (!tableInfo.requirepasswordchange) {
        console.log('Adding requirepasswordchange column to user table...');
        // Add requirepasswordchange column to user table (lowercase to match database convention)
        await queryInterface.addColumn('user', 'requirepasswordchange', {
          type: Sequelize.BOOLEAN,
          allowNull: true, // Make it nullable since it might not exist for all users
          defaultValue: false
        });
        console.log('requirepasswordchange column added successfully.');
      } else {
        console.log('requirepasswordchange column already exists, skipping migration.');
      }
    } catch (error) {
      console.error('Migration error:', error.message);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Check if the requirepasswordchange column exists before trying to remove it (lowercase to match database convention)
      const tableInfo = await queryInterface.describeTable('user');

      if (tableInfo.requirepasswordchange) {
        console.log('Removing requirepasswordchange column from user table...');
        // Remove requirepasswordchange column from user table
        await queryInterface.removeColumn('user', 'requirepasswordchange');
        console.log('requirepasswordchange column removed successfully.');
      } else {
        console.log('requirepasswordchange column does not exist, skipping rollback.');
      }
    } catch (error) {
      console.error('Migration rollback error:', error.message);
      throw error;
    }
  }
};
