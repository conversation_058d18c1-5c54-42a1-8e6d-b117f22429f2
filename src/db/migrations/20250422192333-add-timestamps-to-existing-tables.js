"use strict";

/** @type {import('sequelize-cli').Migration} */
"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    // Approvals table
    await queryInterface.addColumn("approvals", "createdAt", {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    });

    await queryInterface.addColumn("approvals", "updatedAt", {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    });

    // Audit Logs table
    await queryInterface.addColumn("audit_logs", "createdAt", {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    });

    await queryInterface.addColumn("audit_logs", "updatedAt", {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    });

    // User Access Requests table
    await queryInterface.addColumn("UserAccessRequests", "createdAt", {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    });

    await queryInterface.addColumn("UserAccessRequests", "updatedAt", {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    });

    // Revocations table
    await queryInterface.addColumn("Revocations", "createdAt", {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    });

    await queryInterface.addColumn("Revocations", "updatedAt", {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn("approvals", "createdAt");
    await queryInterface.removeColumn("approvals", "updatedAt");

    await queryInterface.removeColumn("audit_logs", "createdAt");
    await queryInterface.removeColumn("audit_logs", "updatedAt");

    await queryInterface.removeColumn("UserAccessRequests", "createdAt");
    await queryInterface.removeColumn("UserAccessRequests", "updatedAt");

    await queryInterface.removeColumn("Revocations", "createdAt");
    await queryInterface.removeColumn("Revocations", "updatedAt");
  },
};
