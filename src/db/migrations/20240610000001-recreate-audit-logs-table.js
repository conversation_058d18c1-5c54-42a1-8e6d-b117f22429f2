'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'audit_logs'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (!tableExists[0].exists) {
        console.log('Table audit_logs does not exist yet. Creating it from scratch.');
        
        // Create the table with all fields including the updated formType ENUM
        await queryInterface.createTable('audit_logs', {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
          },
          userName: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          action: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          formType: {
            type: Sequelize.ENUM('access', 'revocation', 'approval', 'user-management'),
            allowNull: false,
          },
          formId: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          subId: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          systemName: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          branch: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          role: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          approvers: {
            type: Sequelize.JSON,
            allowNull: true,
          },
          ip: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          path: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          method: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          agent: {
            type: Sequelize.STRING,
            allowNull: true,
          },
          timestamp: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.fn('NOW'),
          },
        });
        
        console.log('Successfully created audit_logs table with updated ENUM');
      } else {
        console.log('Table audit_logs already exists. Attempting to update formType ENUM.');
        
        // Try to update the existing table
        try {
          // Create a temporary column with the new type
          await queryInterface.addColumn('audit_logs', 'formType_new', {
            type: Sequelize.ENUM('access', 'revocation', 'approval', 'user-management'),
            allowNull: true
          });
          
          // Copy data from old column to new column
          await queryInterface.sequelize.query(`
            UPDATE audit_logs 
            SET formType_new = formType::text::enum_audit_logs_formtype_new
            WHERE formType IN ('access', 'revocation', 'approval')
          `);
          
          // Drop the old column
          await queryInterface.removeColumn('audit_logs', 'formType');
          
          // Rename the new column to the original name
          await queryInterface.renameColumn('audit_logs', 'formType_new', 'formType');
          
          // Make the column NOT NULL
          await queryInterface.changeColumn('audit_logs', 'formType', {
            type: Sequelize.ENUM('access', 'revocation', 'approval', 'user-management'),
            allowNull: false
          });
          
          console.log('Successfully updated formType ENUM in audit_logs table');
        } catch (error) {
          console.error('Error updating formType ENUM:', error.message);
          console.log('Attempting alternative approach...');
          
          // Alternative approach: try to add the value directly to the enum
          try {
            await queryInterface.sequelize.query(`
              ALTER TYPE enum_audit_logs_formtype ADD VALUE IF NOT EXISTS 'user-management';
            `);
            console.log('Successfully added user-management to formType ENUM');
          } catch (enumError) {
            console.error('Error adding value to ENUM:', enumError.message);
          }
        }
      }
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // We can't easily revert this migration as PostgreSQL doesn't support removing ENUM values
    console.log('Cannot revert this migration as it would require recreating the ENUM type without the new value.');
  }
};
