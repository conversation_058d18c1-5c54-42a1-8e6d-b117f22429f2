"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addIndex("UserAccessRequests", ["subId"]);
    await queryInterface.addIndex("approvals", ["subId", "approverId"]);
    await queryInterface.addIndex("audit_logs", ["subId"]);
    await queryInterface.addIndex("tenant", ["sub", "url"]);
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */

    await queryInterface.removeIndex("UserAccessRequests", ["subId"]);
    await queryInterface.removeIndex("approvals", ["subId", "approverId"]);
    await queryInterface.removeIndex("audit_logs", ["subId"]);
    await queryInterface.removeIndex("tenant", ["sub", "url"]);
  },
};
