'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the user table exists
      const tableExists = await queryInterface.sequelize.query(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'user'
        );`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );
      
      if (!tableExists[0].exists) {
        console.log('Table user does not exist yet. Skipping migration.');
        return;
      }

      // Step 1: Create a new ENUM type for roles
      await queryInterface.sequelize.query(
        `CREATE TYPE "enum_user_role_new" AS ENUM ('systemAdmin', 'hr', 'it', 'supervisor');`
      );

      // Step 2: Add a temporary column with the new ENUM type
      await queryInterface.addColumn('user', 'role_new', {
        type: Sequelize.DataTypes.ENUM('systemAdmin', 'hr', 'it', 'supervisor'),
        allowNull: true
      });

      // Step 3: Migrate data from old role to new role
      await queryInterface.sequelize.query(`
        UPDATE "user" 
        SET role_new = CASE 
          WHEN role = 'admin' THEN 'systemAdmin'::enum_user_role_new
          WHEN role = 'it' THEN 'it'::enum_user_role_new
          ELSE 'supervisor'::enum_user_role_new
        END;
      `);

      // Step 4: Drop the old role column
      await queryInterface.removeColumn('user', 'role');

      // Step 5: Rename the new role column to role
      await queryInterface.renameColumn('user', 'role_new', 'role');

      // Step 6: Add the status column
      await queryInterface.addColumn('user', 'status', {
        type: Sequelize.DataTypes.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active'
      });

      console.log('Successfully updated user roles and added status column');
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Step 1: Create a new ENUM type for the old roles
      await queryInterface.sequelize.query(
        `CREATE TYPE "enum_user_role_old" AS ENUM ('admin', 'it');`
      );

      // Step 2: Add a temporary column with the old ENUM type
      await queryInterface.addColumn('user', 'role_old', {
        type: Sequelize.DataTypes.ENUM('admin', 'it'),
        allowNull: true
      });

      // Step 3: Migrate data from new role to old role
      await queryInterface.sequelize.query(`
        UPDATE "user" 
        SET role_old = CASE 
          WHEN role = 'systemAdmin' THEN 'admin'::enum_user_role_old
          WHEN role = 'it' THEN 'it'::enum_user_role_old
          ELSE 'admin'::enum_user_role_old
        END;
      `);

      // Step 4: Drop the new role column
      await queryInterface.removeColumn('user', 'role');

      // Step 5: Rename the old role column to role
      await queryInterface.renameColumn('user', 'role_old', 'role');

      // Step 6: Remove the status column
      await queryInterface.removeColumn('user', 'status');

      console.log('Successfully reverted user roles and removed status column');
    } catch (error) {
      console.error('Migration reversion error:', error);
      throw error;
    }
  }
};
