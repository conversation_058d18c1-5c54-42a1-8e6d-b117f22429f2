"use strict";
const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Revocation extends Model {
    static associate(models) {
      // Define associations here if needed
    }
  }

  Revocation.init(
    {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    fullName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    employeeId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    jobTitle: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    department: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    contact: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    systems: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: false,
    },
    reason: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    otherReason: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    approverName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    approverJobTitle: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    approverContact: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    signaturePath: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    submittedBy: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM("Pending", "Completed", "Rejected"),
      defaultValue: "Pending",
    },
    actionedByIT: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    actionedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    rejectionReason: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    lastWorkingDay: {
      type: DataTypes.DATEONLY,
      allowNull: true, // for spreadsheet imports
    },
    subsidiary: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    },
    {
      sequelize,
      modelName: "Revocation",
      tableName: "Revocations",
      timestamps: true,
    }
  );

  return Revocation;
};
