'use strict';
const { Model, DataTypes } = require('sequelize');
module.exports = (sequelize) => {
  class Approval extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }

  Approval.init(
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER
      },
      formId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      formType: {
        type: DataTypes.ENUM('access', 'revocation'),
        allowNull: false,
      },
      approverId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM('Pending', 'Approved', 'Rejected'),
        defaultValue: 'Pending',
      },
      comments: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      subId: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: 'Approval',
      tableName: 'approvals',
      timestamps: true
    }
  );

  return Approval;
};
