"use strict";
const { Model, DataTypes, Sequelize } = require("sequelize");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
module.exports = (sequelize) => {
  class User extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Define associations here if needed
    }
  }

  User.init(
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER
      },
      username: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true,
        },
      },
      password: {
        type: DataTypes.STRING,
        allowNull: true,
        set(value) {
          if (value) {
            const salt = bcrypt.genSaltSync(10);
            const hash = bcrypt.hashSync(value, salt);
            this.setDataValue('password', hash);
          }
        },
      },
      role: {
        type: DataTypes.ENUM('systemAdmin', 'hr', 'it', 'supervisor'),
        allowNull: false,

      },
      status: {
        type: DataTypes.ENUM('active', 'inactive'),
        allowNull: false,
        defaultValue: 'active',
      },
      sub: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      subId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        get() {
          // Ensure we return a valid number if the value exists
          const value = this.getDataValue('subId');
          if (value === null || value === undefined) {
            return null;
          }

          const parsedValue = parseInt(value, 10);
          return !isNaN(parsedValue) ? parsedValue : null;
        },
        set(value) {
          // Ensure we store a valid number if the value exists
          if (value === null || value === undefined) {
            this.setDataValue('subId', null);
            return;
          }

          const parsedValue = parseInt(value, 10);
          if (isNaN(parsedValue)) {
            console.error(`Attempted to set invalid subId: ${value}`);
            this.setDataValue('subId', null);
          } else {
            this.setDataValue('subId', parsedValue);
          }
        }
      },
      employerId: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      requirePasswordChange: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
        field: 'requirepasswordchange',
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    },
    {
      sequelize,
      modelName: "User",
      tableName: "user",
      timestamps: true
    }
  );

  // Instance methods
  User.prototype.comparePassword = function(candidatePassword) {
    return bcrypt.compareSync(candidatePassword, this.password);
  };

  User.prototype.generateJWT = function() {
    return jwt.sign(
      {
        id: this.id,
        email: this.email,
        username: this.username,
        role: this.role,
        status: this.status || 'active',
        sub: this.sub,
        subId: (() => {
          if (this.subId === null || this.subId === undefined) {
            return null;
          }
          const parsedValue = parseInt(this.subId, 10);
          return !isNaN(parsedValue) ? parsedValue : null;
        })(), // Ensure subId is a valid integer if present
        employerId: this.employerId || this.email?.split('@')[0] || 'user', // Use email username as fallback
        requirePasswordChange: this.requirePasswordChange === true // Ensure it's a boolean
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '1d' }
    );
  };

  return User;
};
