const axios = require("axios").default;

class UserService {
  constructor({ baseUrl, apiKey }) {
    this.apiKey = apiKey;
    this.baseURL = baseUrl;
    this.mambuClient = axios.create({
      baseURL: baseUrl,
      headers: {
        Accept: "application/vnd.mambu.v2+json",
        ApiKey: apiKey,
      },
    });
  }

  // fetch user details
  async getUser(id) {
    try {
      const user = await this.mambuClient.get(`users/${id}`);
      return user.data;
    } catch (error) {
      console.log(error, "Error fetching user");
    }
  }

  // Fetch branches
  async getBranch(id) {
    try {
      const branch = await this.mambuClient.get(`branches/${id}`);
      return branch.data;
    } catch (error) {
      console.log(error, "kwa getloanBy");
    }
  }

  // fetch roles
  async getRoles() {
    try {
      const response = await this.mambuClient.get("roles");
      return response.data;
    } catch (error) {
      console.log(error, "kwa getRoles");
    }
  }
}

exports.UserService = UserService;
