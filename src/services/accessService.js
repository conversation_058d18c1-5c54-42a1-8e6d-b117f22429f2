const axios = require("axios");
require("dotenv").config();

const SUBSIDIARY_CONFIG = {
  platinumkenya: {
    authUrl: "https://ws.platinumcredit.co.ke/ws/auth/gettoken",
    baseUrl: "https://ws.platinumcredit.co.ke/api/dimension",
    origin: "https://ws.platinumcredit.co.ke",
  },
  premierkenya: {
    authUrl: "https://cache.premiergroup.co.ke/ws/auth/gettoken",
    baseUrl: "https://cache.premiergroup.co.ke/api/dimension",
    origin: "https://cache.premiergroup.co.ke",
  },
  momentumcredit: {
    authUrl: "https://ws.momentumcredit.co.ke/ws/auth/gettoken",
    baseUrl: "https://ws.momentumcredit.co.ke/api/dimension",
    origin: "https://ws.momentumcredit.co.ke",
  },
  platinumtanzania: {
    authUrl: "https://ws.platinumcredit.co.tz/ws/auth/gettoken",
    baseUrl: "https://ws.platinumcredit.co.tz/api/dimension",
    origin: "https://ws.platinumcredit.co.tz",
  },
  premierfanikiwa: {
    authUrl: "https://ws.fmfc.co.tz/ws/auth/gettoken",
    baseUrl: "https://ws.fmfc.co.tz/api/dimension",
    origin: "https://ws.fmfc.co.tz",
  },
  spectrumzambia: {
    authUrl: "https://cache.spectrumcreditltd.com/ws/auth/gettoken",
    baseUrl: "https://cache.spectrumcreditltd.com/api/dimension",
    origin: "https://cache.spectrumcreditltd.com",
  },
  premiersouthafrica: {
    authUrl: "https://ws.premiercredit.co.za/ws/auth/gettoken",
    baseUrl: "https://ws.premiercredit.co.za/api/dimension",
    origin: "https://ws.premiercredit.co.za",
  },
  platinumuganda: {
    authUrl: "https://ws.platinumcredit.co.ug/ws/auth/gettoken",
    baseUrl: "https://ws.platinumcredit.co.ug/api/dimension",
    origin: "https://ws.platinumcredit.co.ug",
  },
  premieruganda: {
    authUrl: "https://ws.premiercredit.co.ug/ws/auth/gettoken",
    baseUrl: "https://ws.premiercredit.co.ug/api/dimension",
    origin: "https://ws.premiercredit.co.ug",
  },

};

class AccessService {
  constructor(subsidiary = "platinumkenya") {
    this.config = SUBSIDIARY_CONFIG[subsidiary];
    if (!this.config) {
      throw new Error(`Unknown subsidiary: ${subsidiary}`);
    }
    this.token = null;
  }

  async authenticate() {
    try {
      const res = await axios.post(
        this.config.authUrl,
        process.env.DIMENSION_API_TOKEN_PLATKE,
        {
          headers: {
            Authorization: `Bearer ${process.env.DIMENSION_API_TOKEN_PLATKE}`,
            Accept: "application/json",
            "Content-Type": "application/json",
            Origin: this.config.origin,
          },
        }
      );
      this.token = res.data;
    } catch (error) {
      console.error(
        "Authentication failed:",
        error?.response?.data || error.message
      );
      throw new Error("Failed to authenticate");
    }
  }

  async fetchDimensionData(endpoint, entityName) {
    try {
      if (!this.token) await this.authenticate();

      const response = await axios.get(`${this.config.baseUrl}/${endpoint}`, {
        headers: {
          Origin: this.config.origin,
          Authorization: `Bearer ${this.token}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      return response.data.dimensions || [];
    } catch (error) {
      this.handleError(error, `Error fetching ${entityName}`);
    }
  }

  async getBranches() {
    return this.fetchDimensionData("branches", "branches");
  }

  async getRoles() {
    return this.fetchDimensionData("roles", "roles");
  }

  async getDepartments() {
    return this.fetchDimensionData("departments", "departments");
  }

  handleError(error, message) {
    console.error(`${message}:`, error.response?.data || error.message);
    throw new Error(message);
  }
}

module.exports = AccessService;
