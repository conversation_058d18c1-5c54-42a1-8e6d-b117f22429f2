// const { Sequelize } = require("sequelize");

// const sequelize = new Sequelize(
//   process.env.DB_DBNAME,
//   process.env.DB_USERNAME,
//   process.env.DB_PASSWORD,
//   {
//     host: process.env.DB_HOST,
//     dialect: process.env.DB_DIALECT,
//     logging: false,
//   },
// );

// const authenticateDatabase = async () => {
//   try {
//     await sequelize.authenticate();
//     console.log("Database connected successfully");
//   } catch (error) {
//     console.error("Unable to connect to the database:", error);
//   }
// };

// module.exports = { sequelize, authenticateDatabase };
