const apiKeys = {
  platinumkenya: process.env.PLATINUM_KE_API_KEY,
  platinumtanzania: process.env.PLATINUM_TZ_API_KEY,
  premierfanikiwa: process.env.FANIKIWA_TZ_API_KEY,
  momentumcredit: process.env.MOMENTUM_KE_API_KEY,
  premierkenya: process.env.PREMIER_KE_API_KEY,

};

function extractMambuUser(signed_request) {
  try {
    // Check if signed_request is defined
    if (!signed_request) {
      console.log('Warning: signed_request is undefined');
      return {
        userId: 'unknown',
        base_url: '',
        mambu_env: 'unknown',
        sub: '',
        domain: '',
        apiKey: ''
      };
    }

    console.log({ signed_request });

    // Check if signed_request has the expected format
    if (!signed_request.includes('.')) {
      console.log('Warning: signed_request does not have the expected format');
      return {
        userId: 'unknown',
        base_url: '',
        mambu_env: 'unknown',
        sub: '',
        domain: '',
        api<PERSON>ey: ''
      };
    }

    // split the base64 grab first item
    let _signed = signed_request.split(".")[1];

    // decode
    const data = Buffer.from(_signed, "base64").toString("utf8");

    // parse JSON
    const decode_obj = JSON.parse(data);
    const userId = decode_obj["USER_KEY"];
    const domain = decode_obj["DOMAIN"];
    const sub = decode_obj["TENANT_ID"];

    const mambu_env = domain.includes("sandbox") ? "sandbox" : "production";
    const base_url = "https://" + domain + "/api";

    const apiKey = apiKeys[sub];

    return { userId, base_url, mambu_env, sub, domain, apiKey };
  } catch (error) {
    console.log('Error extracting Mambu user:', error.message);
    // Return a default object instead of throwing an error
    return {
      userId: 'unknown',
      base_url: '',
      mambu_env: 'unknown',
      sub: '',
      domain: '',
      apiKey: ''
    };
  }
}

module.exports = { extractMambuUser };