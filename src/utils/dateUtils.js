const timezones = require('../config/timezones');

/**
 * Format date with correct timezone for subsidiary
 * @param {Date|string} date - The date to format
 * @param {string} subsidiary - The subsidiary identifier
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date string
 */
const formatDateForSubsidiary = (date, subsidiary = 'platinumkenya', options = {}) => {
  try {
    const dateObj = new Date(date);
    const timezoneConfig = timezones[subsidiary] || timezones.default;
    
    const defaultOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: timezoneConfig.timezone,
      hour12: false // Use 24-hour format
    };

    const formatOptions = { ...defaultOptions, ...options };
    
    return dateObj.toLocaleString('en-US', formatOptions);
  } catch (error) {
    console.error('Error formatting date:', error);
    return new Date(date).toLocaleString();
  }
};

/**
 * Get current date/time for a specific subsidiary
 * @param {string} subsidiary - The subsidiary identifier
 * @returns {string} Current date/time formatted for the subsidiary
 */
const getCurrentTimeForSubsidiary = (subsidiary = 'platinumkenya') => {
  return formatDateForSubsidiary(new Date(), subsidiary);
};

/**
 * Format date for display in reports
 * @param {Date|string} date - The date to format
 * @param {string} subsidiary - The subsidiary identifier
 * @returns {string} Formatted date string for reports
 */
const formatDateForReport = (date, subsidiary = 'platinumkenya') => {
  return formatDateForSubsidiary(date, subsidiary, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

/**
 * Get timezone info for a subsidiary
 * @param {string} subsidiary - The subsidiary identifier
 * @returns {Object} Timezone configuration
 */
const getTimezoneInfo = (subsidiary = 'platinumkenya') => {
  return timezones[subsidiary] || timezones.default;
};

module.exports = {
  formatDateForSubsidiary,
  getCurrentTimeForSubsidiary,
  formatDateForReport,
  getTimezoneInfo
};
