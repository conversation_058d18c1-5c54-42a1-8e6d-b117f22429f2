const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
// const { sequelize } = require("./config/db");
const accessRoutes = require("./routes/accessRoutes");
const auditLogRoutes = require("./routes/auditLogRoutes");
const reportsRoutes = require("./routes/reportRoutes");
const dashboardRoutes = require("./routes/dashboardRoutes");
const revocationRoutes = require("./routes/revocationRoutes");
const authRoutes = require("./routes/auth");
const path = require("path");
const morgan = require("morgan");
const session = require("./utils/session");
const readSession = require("./utils/readSession");
const index = require("./db/models/index.js");
const authJwt = require("./_middlewares/authJwt");
const subsidiaryCheck = require("./_middlewares/subsidiaryCheck");

dotenv.config();

const app = express();

app.use(cors());
app.use(morgan("dev"));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve signature files
app.use("/user-access/v1/uploads", express.static(path.join(__dirname, "../uploads")));
app.use("/user-access/v1/static", express.static(path.join(__dirname, "../public/images")));

const frontendPath = path.join(__dirname, "../public/dist");
app.use(express.static(frontendPath));


const sessions = {};

app.get("/user-access/app-definition/", (req, res) => {
  res.status(200).sendFile(path.join(__dirname, "../public/dev.xml"));
});

app.post("/user-access/ui", (req, res, next) => {
  console.log(req.body);
  const _signed = req.body.signed_request;
  session(_signed);
  req.isrequestvalid = true;
  const signedpart1 = _signed.split(".")[0];
  req.signedToken = signedpart1;
  req.method = "GET";
  next();
});
// Middleware to populate audit data
// Special route for login page - always serve the Vue app without session check
app.get("/user-access/ui/login", (_, res) => {
  console.log("Login page requested");
  res.sendFile(path.join(__dirname, "../public/dist/index.html"));
});

app.use(
  "/user-access/ui",
  (req, res, next) => {
    console.log("Session check for path:", req.path);

    // Skip session check for login page
    if (req.path === "/login") {
      console.log("Login page detected, serving without session check");
      return res.sendFile(path.join(__dirname, "../public/dist/index.html"));
    }

    if (req.isrequestvalid) {
      let expiry = new Date().getTime();
      expiry += 1000 * 2;
      sessions[req.signedToken] = { expiresIn: expiry };
    }
    const mambuUser = JSON.parse(
      readSession() || JSON.stringify({ session: "" }),
    ).session;
    const sess = sessions[mambuUser.split(".")[0]];
    console.log("Session", sess);
    const mambuUserSession = req.header("mambuUser");

    if (sess || mambuUserSession) {
      if ((sess && sess.expiresIn > new Date().getTime()) || mambuUserSession) {
        next();
      } else {
        return res
          .status(404)
          .sendFile(path.join(__dirname, "../public/error404.html"));
      }
    } else {
      return res
        .status(404)
        .sendFile(path.join(__dirname, "../public/error404.html"));
    }
  },

  express.static(path.join(__dirname, "../public/dist")),
);

// Apply JWT authentication and subsidiary check to all API routes except auth
// Auth routes need to be excluded as they handle login/registration
const apiPrefix = "/user-access/v1";
const apiMiddleware = [authJwt, subsidiaryCheck];

// Auth routes (no subsidiary check needed for authentication)
app.use(`${apiPrefix}/auth`, authRoutes);

// Protected API routes with subsidiary check
app.use(`${apiPrefix}/dashboard`, apiMiddleware, dashboardRoutes);
app.use(`${apiPrefix}/reports`, apiMiddleware, reportsRoutes);
app.use(`${apiPrefix}/access-request`, apiMiddleware, accessRoutes);
app.use(`${apiPrefix}/audit-logs`, apiMiddleware, auditLogRoutes);
app.use(`${apiPrefix}/revocation`, apiMiddleware, revocationRoutes);
app.use(`${apiPrefix}/tenant`, apiMiddleware, require("./routes/tenantRoute"));
app.use(`${apiPrefix}/user`, apiMiddleware, require("./routes/userRoute"));
app.use(`${apiPrefix}/create-tenant`, apiMiddleware, require("./routes/tenantRoute"));

index.sequelize.sync({ force: false }).then(() => {
  console.log("Database connected");
});

// Handle 404 errors
app.use((_, res) => {
  res.status(404).send({ error: "Route Not Found" });
});

// Handle other errors globally
app.use((err, _, res, __) => {
  console.error(err);
  res.status(500).send({ error: "Something went wrong!" });
});

module.exports = app;
