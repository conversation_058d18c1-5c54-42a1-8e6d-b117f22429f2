const { Op } = require("sequelize");
const fs = require("fs");
const { format } = require("@fast-csv/format");
const { UserAccessRequest: AccessForm,} = require("../db/models");

const db = require("../db/models");

const Approval = db.Approval;
const AuditLog = db.AuditLog;

// Get Access Form History Report
const getAccessFormHistoryReport = async (req, res) => {
  try {
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase();
    const accessForms = await AccessForm.findAll({
      where: { subId: subsidiary },
    });
    res.status(200).json({ data: accessForms });
  } catch (error) {
    console.error("Error fetching access form history:", error);
    res.status(500).json({ error: "Failed to generate report" });
  }
};

// Get Audit Log Report
const getAuditLogReport = async (req, res) => {
  try {
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase();
    const logs = await AuditLog.findAll({ where: { subId: subsidiary } });
    res.status(200).json({ data: logs });
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    res.status(500).json({ error: "Failed to generate report" });
  }
};

// Get Pending Approval Report
const getPendingApprovalReport = async (req, res) => {
  try {
    const subsidiary = req.headers["x-subsidiary"]?.toLowerCase();
    const pendingApprovals = await Approval.findAll({
      where: { status: "Pending", subId: subsidiary },
    });
    res.status(200).json({ data: pendingApprovals });
  } catch (error) {
    console.error("Error fetching pending approvals:", error);
    res.status(500).json({ error: "Failed to generate report" });
  }
};

// Export Report as CSV using fast-csv
const exportReportAsCSV = async (req, res) => {
  try {
    const { reportType } = req.params;
    const subsidiary = req.query.subId?.toLowerCase();
    let data = [];

    switch (reportType) {
      case "access":
        data = await AccessForm.findAll({
          where: { subId: subsidiary },
          raw: true,
        });
        break;
      case "audit":
        data = await AuditLog.findAll({
          where: { subId: subsidiary },
          raw: true,
        });
        break;
      case "approvals":
        data = await Approval.findAll({
          where: { status: "Pending", subId: subsidiary },
          raw: true,
        });
        break;
      case "revocation":
        data = await RevocationForm.findAll({
          where: { subId: subsidiary },
          raw: true,
        });
        break;
      default:
        return res.status(400).json({ error: "Invalid report type" });
    }

    const csvStream = format({ headers: true });
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${reportType}_report.csv`
    );

    csvStream.pipe(res);
    data.forEach((row) => csvStream.write(row));
    csvStream.end();
  } catch (error) {
    console.error("Error exporting report:", error);
    res.status(500).json({ error: "Failed to export report" });
  }
};

module.exports = {
  getAccessFormHistoryReport,
  getAuditLogReport,
  getPendingApprovalReport,
  exportReportAsCSV,
};
