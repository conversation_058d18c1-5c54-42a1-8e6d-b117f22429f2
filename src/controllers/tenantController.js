const db = require("../db/models");
const Tenant = db.Tenant;
const { v4: uuidv4 } = require('uuid');

/**
 * Get all tenants
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllTenants = async (req, res) => {
  try {
    const tenants = await Tenant.findAll();
    res.status(200).json({
      status: "success",
      data: tenants
    });
  } catch (error) {
    console.error("Error fetching tenants:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch tenants",
      error: error.message
    });
  }
};

/**
 * Get a single tenant by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTenantById = async (req, res) => {
  try {
    const tenant = await Tenant.findByPk(req.params.id);
    if (!tenant) {
      return res.status(404).json({
        status: "fail",
        message: "Tenant not found"
      });
    }
    res.status(200).json({
      status: "success",
      data: tenant
    });
  } catch (error) {
    console.error("Error fetching tenant:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch tenant",
      error: error.message
    });
  }
};

/**
 * Create a new tenant
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createTenant = async (req, res) => {
  try {
    // Validate required fields
    const { sub, url, name } = req.body;

    if (!sub || !url || !name) {
      return res.status(400).json({
        status: "fail",
        message: "Missing required fields: sub, url, and name are required"
      });
    }

    // Check if tenant with same sub already exists
    const existingTenant = await Tenant.findOne({ where: { sub } });
    if (existingTenant) {
      return res.status(400).json({
        status: "fail",
        message: `Tenant with sub '${sub}' already exists`
      });
    }

    // Generate uniqueId if not provided
    if (!req.body.uniqueId) {
      req.body.uniqueId = `${sub}-${uuidv4().substring(0, 8)}`;
    }

    // Create the tenant
    const tenant = await Tenant.create(req.body);

    res.status(201).json({
      status: "success",
      data: tenant
    });
  } catch (error) {
    console.error("Error creating tenant:", error);
    res.status(400).json({
      status: "fail",
      message: "Failed to create tenant",
      error: error.message
    });
  }
};

/**
 * Update a tenant by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateTenant = async (req, res) => {
  try {
    // Find the tenant first
    const tenant = await Tenant.findByPk(req.params.id);

    if (!tenant) {
      return res.status(404).json({
        status: "fail",
        message: "Tenant not found"
      });
    }

    // If trying to update sub, check if it already exists
    if (req.body.sub && req.body.sub !== tenant.sub) {
      const existingTenant = await Tenant.findOne({
        where: { sub: req.body.sub }
      });

      if (existingTenant) {
        return res.status(400).json({
          status: "fail",
          message: `Tenant with sub '${req.body.sub}' already exists`
        });
      }
    }

    // Update the tenant
    await tenant.update(req.body);

    res.status(200).json({
      status: "success",
      data: tenant
    });
  } catch (error) {
    console.error("Error updating tenant:", error);
    res.status(400).json({
      status: "fail",
      message: "Failed to update tenant",
      error: error.message
    });
  }
};

/**
 * Delete a tenant by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteTenant = async (req, res) => {
  try {
    const tenant = await Tenant.findByPk(req.params.id);

    if (!tenant) {
      return res.status(404).json({
        status: "fail",
        message: "Tenant not found"
      });
    }

    // Check if tenant has associated users
    const userCount = await db.User.count({ where: { subId: tenant.id } });

    if (userCount > 0) {
      return res.status(400).json({
        status: "fail",
        message: `Cannot delete tenant with ${userCount} associated users. Please delete or reassign users first.`
      });
    }

    // Delete the tenant
    await tenant.destroy();

    res.status(200).json({
      status: "success",
      message: "Tenant deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting tenant:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to delete tenant",
      error: error.message
    });
  }
};
