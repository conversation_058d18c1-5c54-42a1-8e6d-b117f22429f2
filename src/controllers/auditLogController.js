const { UserAccessRequest, AuditLog } = require("../db/models");
const approverEmails = require("../config/approverEmails");

const createAuditLog = async (
  userName,
  action,
  formType,
  formId,
  createdBy,
  details = {}
) => {
  try {
    console.log("Creating Audit Log...");

    const userRequest = await UserAccessRequest.findByPk(formId);
    if (!userRequest) {
      console.warn("UserAccessRequest not found for audit log:", formId);
      return;
    }

    const approvers = details.approvers || [];

    // Get the subsidiary
    const subId = details.subId || userRequest.subId || "platinumkenya";

    // Get approver emails for this subsidiary
    const subsidiaryApprovers = approverEmails[subId] || {};

    // If this is an approval action, add the approver with their email
    if (action.includes("Approved by HR") || action === "HR Approval") {
      approvers.push(`HR (${subsidiaryApprovers.HR || 'Unknown Email'})`);
    } else if (action.includes("Approved by Executive") || action === "Executive Approval") {
      approvers.push(`EXECUTIVE (${subsidiaryApprovers.EXECUTIVE || 'Unknown Email'})`);
    } else if (action.includes("Approved by IT") || action === "IT Approval") {
      approvers.push(`IT (${subsidiaryApprovers.IT || 'Unknown Email'})`);
    } else if (action.includes("Approved Request")) {
      approvers.push(action);
    }

    const logEntry = {
      userName: `${userRequest.firstName} ${userRequest.lastName}`,
      action,
      formType,
      formId,
      systemName: userRequest.systemName,
      branch: userRequest.branch,
      department: userRequest.department,
      role: userRequest.role,
      createdBy: createdBy || "<EMAIL>",
      approvers: JSON.stringify(approvers),
      timestamp: new Date(),
      subId,
      // You can add additional request data: ip, method, path, etc., from details if needed
    };

    const auditLog = await AuditLog.create(logEntry);
    console.log("Audit Log Created:", auditLog);

    return auditLog;
  } catch (error) {
    console.error("Error creating audit log:", error);
  }
};

// Fetch all audit logs filtered by subId
const getAuditLogs = async (subId) => {
  try {
    return await AuditLog.findAll({
      where: { subId },
      order: [["id", "DESC"]],
    });
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    throw error;
  }
};

// Get audit logs by formId, optionally filtered by subId
const getAuditLogsByFormId = async (formId, subId = null) => {
  try {
    const where = { formId };
    if (subId) where.subId = subId;

    return await AuditLog.findAll({ where });
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    throw error;
  }
};

module.exports = {
  createAuditLog,
  getAuditLogs,
  getAuditLogsByFormId,
};
