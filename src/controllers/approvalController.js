const RevocationForm = require("../models/revocationForm");
const { Approval, AccessForm, } = require("../db/models");
const { createAuditLog } = require("./auditLogController");

// Approve or Reject a Form
const processApproval = async (req, res) => {
  try {
    const subsidiary =
      req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";
    const { formId, formType, approverId, status, comments } = req.body;

    if (!formId || !formType || !approverId || !status) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    if (!["Approved", "Rejected"].includes(status)) {
      return res.status(400).json({ error: "Invalid status value" });
    }

    const approval = await Approval.create({
      formId,
      formType,
      approverId,
      status,
      comments,
      subId: subsidiary,
    });

    await createAuditLog(
      approverId,
      `${status} Form`,
      "approval",
      formId,
      subsidiary
    );

    if (formType === "access") {
      await AccessForm.update(
        { status },
        { where: { id: formId, subId: subsidiary } }
      );
    } else if (formType === "revocation") {
      await RevocationForm.update(
        { status },
        { where: { id: formId, subId: subsidiary } }
      );
    }

    res
      .status(201)
      .json({ message: `Form ${status} successfully`, data: approval });
  } catch (error) {
    console.error("Error processing approval:", error);
    res
      .status(500)
      .json({ error: "Failed to process approval", details: error.message });
  }
};

// Get All Pending Approvals
const getPendingApprovals = async (req, res) => {
  try {
    const subsidiary =
      req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";
    const approvals = await Approval.findAll({
      where: { status: "Pending", subId: subsidiary },
      order: [["createdAt", "DESC"]],
    });
    res.status(200).json({ data: approvals });
  } catch (error) {
    console.error("Error fetching approvals:", error);
    res.status(500).json({ error: "Failed to fetch approvals" });
  }
};

// Get Approval by ID
const getApprovalById = async (req, res) => {
  try {
    const subsidiary =
      req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";
    const approval = await Approval.findOne({
      where: {
        id: req.params.id,
        subId: subsidiary,
      },
    });
    if (!approval) return res.status(404).json({ error: "Approval not found" });

    res.status(200).json({ data: approval });
  } catch (error) {
    console.error("Error fetching approval:", error);
    res.status(500).json({ error: "Failed to fetch approval" });
  }
};

module.exports = {
  processApproval,
  getPendingApprovals,
  getApprovalById,
};
