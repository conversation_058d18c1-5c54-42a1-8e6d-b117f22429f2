const express = require("express");
const authController = require("../controllers/authController");
const authMiddleware = require("../_middlewares/authJwt");
const roleCheck = require("../_middlewares/roleCheck");

const router = express.Router();

// Public authentication routes
router.post("/login", authController.login);
router.post("/verify-token", authController.verifyToken);

// Protected routes - require authentication
router.use(authMiddleware);

// Register route - only systemAdmin can access
router.post("/register", roleCheck("systemAdmin"), authController.register);

// Get current user
router.get("/me", authController.getCurrentUser);

// Change password route
router.post("/change-password", authController.changePassword);

// Check if password change is required
router.get("/check-password-change", authController.checkPasswordChangeRequired);

// Legacy token validation route
router.post("/validate-token", authController.verifyToken);

module.exports = router;
