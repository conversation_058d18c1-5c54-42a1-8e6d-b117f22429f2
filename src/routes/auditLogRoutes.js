const express = require("express");
const router = express.Router();
const {
  createAuditLog,
  getAuditLogs,
  getAuditLogsByFormId,
} = require("../controllers/auditLogController");

// Helper to get subsidiary from request headers
const getSubsidiaryFromHeader = (req) =>
  req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";

// Create Audit Log
router.post("/", async (req, res) => {
  try {
    const { userId, action, formType, formId } = req.body;
    const subId = getSubsidiaryFromHeader(req);

    if (!userId || !action || !formType || !formId) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    const auditLog = await createAuditLog(
      userId,
      action,
      formType,
      formId,
      req.user?.email,
      {},
      subId
    );
    return res.status(201).json({
      message: "Audit log created successfully",
      auditLog,
    });
  } catch (error) {
    console.error("Error creating audit log:", error);
    res.status(500).json({
      error: "Failed to create audit log",
      details: error.message,
    });
  }
});

// Fetch all Audit Logs for a subsidiary
router.get("/", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);
    const auditLogs = await getAuditLogs(subId);
    return res.status(200).json(auditLogs);
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    res.status(500).json({
      error: "Failed to fetch audit logs",
      details: error.message,
    });
  }
});

// Fetch Audit Logs by Form ID for a subsidiary
router.get("/form/:formId", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);
    const auditLogs = await getAuditLogsByFormId(req.params.formId, subId);
    return res.status(200).json(auditLogs);
  } catch (error) {
    console.error("Error fetching audit logs by form ID:", error);
    res.status(500).json({
      error: "Failed to fetch audit logs",
      details: error.message,
    });
  }
});

module.exports = router;
