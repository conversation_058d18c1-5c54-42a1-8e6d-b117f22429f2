const express = require("express");
const {
  processApproval,
  getPendingApprovals,
  getApprovalById,
} = require("../controllers/approvalController");

const router = express.Router();

// Middleware to extract subsidiary
const extractSubsidiary = (req, res, next) => {
  req.subId = req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";
  next();
};

// Approve or Reject a Form
router.post("/", extractSubsidiary, processApproval);
router.get("/pending", extractSubsidiary, getPendingApprovals);
router.get("/:id", getApprovalById);

module.exports = router;
