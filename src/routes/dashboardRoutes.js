const express = require("express");
const { Op } = require("sequelize");
const { UserAccessRequest } = require("../db/models");
const { Approval } = require("../db/models");
const { AuditLog } = require("../db/models");

const router = express.Router();

const getSubsidiaryFromHeader = (req) =>
  req.headers["x-subsidiary"]?.toLowerCase() || "platinumkenya";

router.get("/dashboard-summary", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);

    const [totalUsers, pendingRequests, approvedRequests] = await Promise.all([
      UserAccessRequest.count({ where: { subId } }),
      UserAccessRequest.count({ where: { approvalStatus: "Pending", subId } }),
      UserAccessRequest.count({
        where: {
          approvalStatus: { [Op.iLike]: "Approved%" },
          subId,
        },
      }),
    ]);

    res.json({
      totalUsers,
      pendingRequests,
      approvedRequests,
    });
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    res.status(500).json({ error: "Failed to fetch dashboard data" });
  }
});

router.get("/recent-activities", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);

    const [accessRequests, approvals] = await Promise.all([
      UserAccessRequest.findAll({
        where: { subId },
        order: [["createdAt", "DESC"]],
        limit: 5,
      }),
      Approval.findAll({
        where: { subId },
        order: [["createdAt", "DESC"]],
        limit: 5,
      }),
    ]);

    res.status(200).json({
      accessRequests,
      approvals,
    });
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    res.status(500).json({ error: "Failed to fetch recent activities" });
  }
});

router.get("/activity-tracker", async (req, res) => {
  try {
    const subId = getSubsidiaryFromHeader(req);

    const [totalRequests, pendingRequests, approvedRequests] =
      await Promise.all([
        UserAccessRequest.count({ where: { subId } }),
        UserAccessRequest.count({
          where: { approvalStatus: "Pending", subId },
        }),
        UserAccessRequest.count({
          where: {
            approvalStatus: { [Op.iLike]: "Approved%" },
            subId,
          },
        }),
      ]);

    const processingRequests =
      totalRequests - (pendingRequests + approvedRequests);

    res.status(200).json({
      totalRequests,
      pendingRequests,
      approvedRequests,
      processingRequests,
    });
  } catch (error) {
    console.error("Error fetching activity tracker data:", error);
    res.status(500).json({ error: "Failed to fetch activity tracker data" });
  }
});

module.exports = router;
