const express = require("express");
const tenantController = require("../controllers/tenantController");
const authJwt = require("../_middlewares/authJwt");
const roleCheck = require("../_middlewares/roleCheck");

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authJwt);

// Get all tenants - only systemAdmin can access
router.get("/", role<PERSON><PERSON><PERSON>("systemAdmin"), tenantController.getAllTenants);

// Get a single tenant by ID - only systemAdmin can access
router.get("/:id", role<PERSON>he<PERSON>("systemAdmin"), tenantController.getTenantById);

// Create a new tenant - only systemAdmin can access
router.post("/", roleCheck("systemAdmin"), tenantController.createTenant);

// Update a tenant - only systemAdmin can access
router.put("/:id", role<PERSON><PERSON><PERSON>("systemAdmin"), tenantController.updateTenant);

// Delete a tenant - only systemAdmin can access
router.delete("/:id", role<PERSON>he<PERSON>("systemAdmin"), tenantController.deleteTenant);

module.exports = router;
