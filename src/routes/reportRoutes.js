const express = require("express");
const router = express.Router();
const {
  getAccessFormHistoryReport,
  getAuditLogReport,
  getPendingApprovalReport,
  exportReportAsCSV,
} = require("../controllers/reportController");

// Middleware to attach subId to request object from x-subsidiary header
router.use((req, res, next) => {
  const header = req.headers["x-subsidiary"];
  req.subId = header ? header.toLowerCase() : "platinumkenya";
  next();
});

// Fetch reports
router.get("/access-history", getAccessFormHistoryReport);
router.get("/audit-logs", getAuditLogReport);
router.get("/pending-approvals", getPendingApprovalReport);

// Export CSV reports
router.get("/export/:reportType", exportReportAsCSV);

module.exports = router;
