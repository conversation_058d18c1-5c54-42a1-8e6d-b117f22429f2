const express = require("express");
const userController = require("../controllers/userController");
const roleCheck = require("../_middlewares/roleCheck");
const userAuditData = require("../_middlewares/userAuditData");
const auditTrail = require("../_middlewares/auditTrail");

const router = express.Router();

// Note: JWT authentication and subsidiary check are now applied globally in app.js

// Get all users - only systemAdmin can access
// Note: The controller will filter users by subsidiary unless the user is systemAdmin
router.get("/", roleCheck('systemAdmin'), userController.getAllUsers);

// Create new user - only systemAdmin can access
router.post("/",
  roleCheck('systemAdmin'),
  userAuditData('user-create'),
  auditTrail('user-management'),
  userController.createUser
);

// Get single user - only systemAdmin can access
router.get("/:id", roleCheck('systemAdmin'), userController.getUser);

// Update user - only systemAdmin can access
router.put("/:id",
  roleCheck('systemAdmin'),
  userAuditData('user-update'),
  auditTrail('user-management'),
  userController.updateUser
);

// Delete/deactivate user - only systemAdmin can access
router.delete("/:id",
  roleCheck('systemAdmin'),
  userAuditData('user-delete'),
  auditTrail('user-management'),
  userController.deleteUser
);

module.exports = router;
