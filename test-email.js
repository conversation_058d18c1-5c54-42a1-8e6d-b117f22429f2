#!/usr/bin/env node

/**
 * Email Testing Script for Revocation System
 * 
 * This script helps debug email configuration issues by:
 * 1. Checking environment variables
 * 2. Testing email service functionality
 * 3. Validating approver email configurations
 * 
 * Usage: node test-email.js [subsidiary] [test-email]
 * Example: node test-email.<NAME_EMAIL>
 */

require('dotenv').config();

const emailService = require('./src/utils/emailService');
const { approverEmails } = require('./src/config/approverEmails');

// Get command line arguments
const subsidiary = process.argv[2] || 'platinumkenya';
const testEmail = process.argv[3];

console.log('🔍 EMAIL CONFIGURATION DIAGNOSTIC');
console.log('=====================================');

// 1. Check Environment Variables
console.log('\n📋 Environment Variables:');
console.log(`EMAIL_API_URL: ${process.env.EMAIL_API_URL || '❌ NOT SET'}`);
console.log(`EMAIL_API_KEY: ${process.env.EMAIL_API_KEY ? '✅ SET' : '❌ NOT SET'}`);
console.log(`EMAIL_USER: ${process.env.EMAIL_USER || '❌ NOT SET'}`);
console.log(`EMAIL_PASS: ${process.env.EMAIL_PASS ? '✅ SET' : '❌ NOT SET'}`);

// 2. Check Approver Email Configuration
console.log('\n📧 Approver Email Configuration:');
console.log(`Testing subsidiary: ${subsidiary}`);

const approvers = approverEmails[subsidiary];
if (!approvers) {
  console.log(`❌ No configuration found for subsidiary: ${subsidiary}`);
  console.log(`Available subsidiaries:`, Object.keys(approverEmails));
  process.exit(1);
} else {
  console.log(`✅ Configuration found for ${subsidiary}:`);
  console.log(`  HR: ${approvers.HR || '❌ NOT SET'}`);
  console.log(`  IT: ${approvers.IT || '❌ NOT SET'}`);
}

// 3. Test Email Sending
async function testEmailSending() {
  console.log('\n📤 Testing Email Sending:');
  
  const testRecipient = testEmail || approvers.IT;
  if (!testRecipient) {
    console.log('❌ No test email provided and no IT email configured');
    return;
  }

  console.log(`Sending test email to: ${testRecipient}`);

  try {
    const result = await emailService.sendEmail({
      to: testRecipient,
      subject: `Test Email - Revocation System (${subsidiary})`,
      tenant: subsidiary,
      emailBody: `
        <h3>🧪 Test Email from Revocation System</h3>
        <p>Dear Team,</p>
        <p>This is a test email to verify the revocation email system is working correctly.</p>
        <table style="border-collapse: collapse; width: 100%; margin: 10px 0;">
          <tr><td style="font-weight: bold;">Subsidiary:</td><td>${subsidiary}</td></tr>
          <tr><td style="font-weight: bold;">Test Time:</td><td>${new Date().toISOString()}</td></tr>
          <tr><td style="font-weight: bold;">Environment:</td><td>${process.env.NODE_ENV || 'development'}</td></tr>
        </table>
        <p>If you received this email, the revocation email system is working correctly!</p>
        <p>Best regards,<br>Digital User Access System</p>
      `,
    });

    console.log('✅ Test email sent successfully!');
    console.log('📊 Response:', result?.status || 'No status available');
  } catch (error) {
    console.log('❌ Test email failed:');
    console.log(`Error: ${error.message}`);
    
    if (error.response) {
      console.log(`HTTP Status: ${error.response.status}`);
      console.log(`Response Data:`, error.response.data);
    }
  }
}

// 4. Test Revocation Email Template
async function testRevocationEmail() {
  console.log('\n📋 Testing Revocation Email Template:');
  
  const testRecipient = testEmail || approvers.IT;
  if (!testRecipient) {
    console.log('❌ No test email provided and no IT email configured');
    return;
  }

  console.log(`Sending test revocation email to: ${testRecipient}`);

  try {
    const result = await emailService.sendEmail({
      to: testRecipient,
      subject: `TEST: Revocation Request Approved by HR - John Doe (${subsidiary})`,
      tenant: subsidiary,
      emailBody: `
        <h3>Revocation Request Ready for IT Approval</h3>
        <p>Dear IT Team,</p>
        <p>A revocation request has been approved by HR and is now ready for your review and final approval.</p>
        <table style="border-collapse: collapse; width: 100%; margin: 10px 0;">
          <tr><td style="font-weight: bold;">Employee Name:</td><td>John Doe (TEST)</td></tr>
          <tr><td style="font-weight: bold;">Employee ID:</td><td>TEST001</td></tr>
          <tr><td style="font-weight: bold;">Department:</td><td>Test Department</td></tr>
          <tr><td style="font-weight: bold;">Systems:</td><td>All Systems</td></tr>
          <tr><td style="font-weight: bold;">Reason:</td><td>Testing Email System</td></tr>
        </table>
        <p><a href="https://uat-uap.platcorpgroup.com/user-access/ui/removal/${subsidiary}" target="_blank">Click here to review and approve this revocation</a></p>
        <p>Best regards,<br>${subsidiary} Access Management System</p>
      `,
    });

    console.log('✅ Test revocation email sent successfully!');
    console.log('📊 Response:', result?.status || 'No status available');
  } catch (error) {
    console.log('❌ Test revocation email failed:');
    console.log(`Error: ${error.message}`);
  }
}

// Run the tests
async function runTests() {
  try {
    await testEmailSending();
    await testRevocationEmail();
    
    console.log('\n🎉 Email diagnostic complete!');
    console.log('\n💡 Troubleshooting Tips:');
    console.log('1. Ensure EMAIL_API_URL and EMAIL_API_KEY are set correctly');
    console.log('2. Check if the external email API service is accessible');
    console.log('3. Verify EMAIL_USER and EMAIL_PASS for SMTP fallback');
    console.log('4. Check network connectivity to email services');
    console.log('5. Review server logs for detailed error messages');
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error.message);
    process.exit(1);
  }
}

// Start the diagnostic
runTests();
