server {
    client_max_body_size 10M;
    server_name server.example.com;

    location /xml/ {
        root /var/www/server.example.com/html;
        index dev.xml;
    }

    location / {
        include proxy_params;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_pass http://localhost:3000/;
    }
}