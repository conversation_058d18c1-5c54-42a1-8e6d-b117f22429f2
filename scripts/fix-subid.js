#!/usr/bin/env node

/**
 * This script fixes the subId column in the user table
 * It changes the column type from STRING to INTEGER
 */

const { Sequelize } = require('sequelize');
const config = require('../src/db/config/config');

// Get the environment
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Create a new Sequelize instance
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    logging: console.log
  }
);

async function fixSubIdColumn() {
  try {
    // Connect to the database
    await sequelize.authenticate();
    console.log('Connected to the database');

    // Check if the subid column exists and get its type
    const [columnInfo] = await sequelize.query(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'user' AND column_name = 'subid';
    `);

    if (columnInfo.length === 0) {
      console.error('subid column not found in user table');
      return;
    }

    console.log('Current subid column info:', columnInfo[0]);

    // Check if the column is already an integer
    if (columnInfo[0].data_type === 'integer') {
      console.log('subid column is already an integer, no need to fix');
      return;
    }

    // Create a temporary column to store the integer values
    console.log('Creating temporary column subid_int...');
    await sequelize.query(`
      ALTER TABLE "user" ADD COLUMN subid_int INTEGER;
    `);

    // Update the temporary column with integer values from subid
    console.log('Updating temporary column with integer values...');
    await sequelize.query(`
      UPDATE "user" 
      SET subid_int = CASE 
        WHEN subid ~ E'^\\\\d+$' THEN subid::INTEGER 
        ELSE NULL 
      END;
    `);

    // Drop the old subid column
    console.log('Dropping old subid column...');
    await sequelize.query(`
      ALTER TABLE "user" DROP COLUMN subid;
    `);

    // Rename the temporary column to subid
    console.log('Renaming temporary column to subid...');
    await sequelize.query(`
      ALTER TABLE "user" RENAME COLUMN subid_int TO subid;
    `);

    console.log('Successfully fixed subid column');

    // Close the connection
    await sequelize.close();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error fixing subid column:', error);
    process.exit(1);
  }
}

// Run the function
fixSubIdColumn();
