'use strict';
const bcrypt = require('bcryptjs');
const { Sequelize } = require('sequelize');
const config = require('../src/db/config/config');

// Get the configuration for the current environment
const env = process.env.NODE_ENV || 'production';
const dbConfig = config[env];

// Create a new Sequelize instance
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    logging: console.log,
  }
);

async function fixPremierkenyaAccess() {
  try {
    // Connect to the database
    await sequelize.authenticate();
    console.log('Connection to the database has been established successfully.');

    // 1. First, check if premierkenya tenant exists
    const [tenants] = await sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premierkenya'`
    );

    if (tenants.length === 0) {
      console.log('Premier Kenya tenant not found. Creating it now...');
      
      // Create the premierkenya tenant
      await sequelize.query(`
        INSERT INTO tenant (
          sub,
          url,
          matcher,
          name,
          uniqueId,
          createdAt,
          updatedAt
        )
        VALUES (
          'premierkenya',
          'https://premierkenya.co.ke',
          'premierkenya.co.ke',
          'Premier Kenya IT Department',
          'premierke-${Date.now()}',
          NOW(),
          NOW()
        )
        RETURNING *;
      `);
      
      console.log('Premier Kenya tenant created successfully.');
    }
    
    // Get the premierkenya tenant ID
    const [updatedTenants] = await sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premierkenya'`
    );
    
    if (updatedTenants.length === 0) {
      console.log('Failed to create Premier Kenya tenant. Please check the database.');
      return;
    }
    
    const tenantId = updatedTenants[0].id;
    console.log(`Found Premier Kenya tenant with ID: ${tenantId}`);

    // 2. Create admin user for premierkenya
    const salt = bcrypt.genSaltSync(10);
    const password = 'Admin@123';
    const hashedPassword = bcrypt.hashSync(password, salt);
    
    // Check if user already exists
    const [existingUsers] = await sequelize.query(
      `SELECT * FROM "user" WHERE email = '<EMAIL>'`
    );
    
    if (existingUsers.length === 0) {
      console.log('Creating admin user for Premier Kenya...');
      
      // Create the admin user
      await sequelize.query(`
        INSERT INTO "user" (
          username,
          email,
          password,
          sub,
          subid,
          employerid,
          role,
          status,
          createdat,
          updatedat
        )
        VALUES (
          'admin',
          '<EMAIL>',
          '${hashedPassword}',
          'premierkenya',
          '${tenantId}',
          'EMP001',
          'systemAdmin',
          'active',
          NOW(),
          NOW()
        )
        RETURNING *;
      `);
      
      console.log('Admin user created successfully for Premier Kenya.');
    } else {
      console.log('Admin user already exists for Premier Kenya.');
      
      // Update the user to ensure it has the correct subsidiary and tenant ID
      await sequelize.query(`
        UPDATE "user"
        SET 
          sub = 'premierkenya',
          subid = '${tenantId}'
        WHERE email = '<EMAIL>'
        RETURNING *;
      `);
      
      console.log('Admin user updated successfully for Premier Kenya.');
    }
    
    // 3. Create or update other users for premierkenya
    const userRoles = [
      { username: 'hr', email: '<EMAIL>', role: 'hr', password: 'Hr@123' },
      { username: 'it', email: '<EMAIL>', role: 'it', password: 'It@123' },
      { username: 'supervisor', email: '<EMAIL>', role: 'supervisor', password: 'Supervisor@123' }
    ];
    
    for (const userRole of userRoles) {
      const [existingUser] = await sequelize.query(
        `SELECT * FROM "user" WHERE email = '${userRole.email}'`
      );
      
      if (existingUser.length === 0) {
        console.log(`Creating ${userRole.role} user for Premier Kenya...`);
        
        // Create the user
        await sequelize.query(`
          INSERT INTO "user" (
            username,
            email,
            password,
            sub,
            subid,
            employerid,
            role,
            status,
            createdat,
            updatedat
          )
          VALUES (
            '${userRole.username}',
            '${userRole.email}',
            '${bcrypt.hashSync(userRole.password, salt)}',
            'premierkenya',
            '${tenantId}',
            'EMP00${userRoles.indexOf(userRole) + 2}',
            '${userRole.role}',
            'active',
            NOW(),
            NOW()
          )
          RETURNING *;
        `);
        
        console.log(`${userRole.role} user created successfully for Premier Kenya.`);
      } else {
        console.log(`${userRole.role} user already exists for Premier Kenya.`);
        
        // Update the user to ensure it has the correct subsidiary and tenant ID
        await sequelize.query(`
          UPDATE "user"
          SET 
            sub = 'premierkenya',
            subid = '${tenantId}'
          WHERE email = '${userRole.email}'
          RETURNING *;
        `);
        
        console.log(`${userRole.role} user updated successfully for Premier Kenya.`);
      }
    }
    
    console.log('\nAll users have been created or updated for Premier Kenya.');
    console.log('\nLogin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin@123');
    console.log('Subsidiary: premierkenya');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await sequelize.close();
  }
}

// Run the function
fixPremierkenyaAccess();
