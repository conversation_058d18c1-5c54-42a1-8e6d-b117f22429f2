/**
 * <PERSON><PERSON><PERSON> to create admin users for all subsidiaries in production
 * Run with: NODE_ENV=production node scripts/create-admin-users.js
 */

require('dotenv').config();
const bcrypt = require('bcryptjs');
const { Sequelize } = require('sequelize');
const config = require('../src/db/config/config');

// Get database configuration based on environment
const dbConfig = process.env.NODE_ENV === 'production'
  ? config.production
  : config.development;

// Create Sequelize instance
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    logging: console.log
  }
);

// List of subsidiaries
const subsidiaries = [
  { id: 1, name: 'platinumkenya', displayName: 'Platinum Kenya' },
  { id: 2, name: 'premierkenya', displayName: 'Premier Kenya' },
  { id: 3, name: 'momentumcredit', displayName: 'Momentum Credit' },
  { id: 4, name: 'platinumtanzania', displayName: 'Platinum Tanzania' },
  { id: 5, name: 'premierfaniki<PERSON>', displayName: 'Premier Fanikiwa' },
  { id: 6, name: 'platinumuganda', displayName: 'Platinum Uganda' },
  { id: 7, name: 'premieruganda', displayName: 'Premier Uganda' },
  { id: 8, name: 'spectrumzambia', displayName: 'Spectrum Zambia' },
  { id: 9, name: 'premiersouthafrica', displayName: 'Premier South Africa' }
];

// Default admin password - CHANGE THIS IN PRODUCTION!
const defaultPassword = 'Admin@123';

// Function to create admin users
async function createAdminUsers() {
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Create admin user for each subsidiary
    for (const subsidiary of subsidiaries) {
      try {
        // Hash the password
        const hashedPassword = await bcrypt.hash(defaultPassword, 10);

        // Create admin user with the correct role value from the model (systemAdmin)
        const [user, created] = await sequelize.query(`
          INSERT INTO "user" (username, email, password, role, status, sub, "subId", "employerId", "createdAt", "updatedAt")
          VALUES ('Admin ; User', 'admin@${subsidiary.name}.com', '${hashedPassword}', 'systemAdmin', 'active', '${subsidiary.name}', ${subsidiary.id}, '${subsidiary.id}', NOW(), NOW())
          ON CONFLICT (email) DO UPDATE
          SET password = '${hashedPassword}', "updatedAt" = NOW()
          RETURNING id;
        `, { type: sequelize.QueryTypes.INSERT });

        if (created) {
          console.log(`Admin user created for ${subsidiary.displayName}`);
        } else {
          console.log(`Admin user updated for ${subsidiary.displayName}`);
        }
      } catch (error) {
        console.error(`Error creating admin user for ${subsidiary.displayName}:`, error.message);
      }
    }

    console.log('\nAdmin users created successfully!');
    console.log('\nLogin credentials for each subsidiary:');
    subsidiaries.forEach(sub => {
      console.log(`- ${sub.displayName}: admin@${sub.name}.com / ${defaultPassword}`);
    });
    console.log('\nIMPORTANT: Change these passwords immediately after first login!');

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    // Close database connection
    await sequelize.close();
  }
}

// Run the function
createAdminUsers();
