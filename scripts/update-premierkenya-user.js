'use strict';
const { Sequelize } = require('sequelize');
const config = require('../src/db/config/config');

// Get the configuration for the current environment
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Create a new Sequelize instance
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    logging: console.log,
  }
);

async function updatePremierkenyaUser() {
  try {
    // Connect to the database
    await sequelize.authenticate();
    console.log('Connection to the database has been established successfully.');

    // Get the premierkenya tenant ID
    const [tenants] = await sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premierkenya'`
    );

    if (tenants.length === 0) {
      console.log('Premier Kenya tenant not found. Please run the tenant seeder first.');
      return;
    }

    const tenantId = tenants[0].id;
    console.log(`Found Premier Kenya tenant with ID: ${tenantId}`);

    // Check if user exists
    const [existingUsers] = await sequelize.query(
      `SELECT * FROM "user" WHERE email = '<EMAIL>'`
    );

    if (existingUsers.length === 0) {
      console.log('User not found. Please run the user seeder first.');
      return;
    }

    console.log('Found existing user:', existingUsers[0]);

    // Update the user to ensure it has the correct subsidiary and tenant ID
    const [result] = await sequelize.query(`
      UPDATE "user"
      SET
        sub = 'premierkenya',
        subid = '${tenantId}'
      WHERE email = '<EMAIL>'
      RETURNING *;
    `);

    console.log('User updated successfully:', result);
    console.log('\nLogin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin@123');
    console.log('Subsidiary: premierkenya');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await sequelize.close();
  }
}

// Run the function
updatePremierkenyaUser();
