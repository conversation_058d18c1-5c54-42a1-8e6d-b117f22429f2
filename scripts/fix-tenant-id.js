#!/usr/bin/env node

/**
 * This script fixes the issue with tenant IDs in the user table
 * It ensures that the subId column is properly set to the tenant ID
 */

const db = require('../src/db/models');
const { Tenant, User } = db;

async function fixTenantIds() {
  try {
    console.log('Starting tenant ID fix...');

    // Get all tenants
    const tenants = await Tenant.findAll();
    console.log(`Found ${tenants.length} tenants`);

    // Create a map of subsidiary names to tenant IDs
    const subToIdMap = {};
    tenants.forEach(tenant => {
      subToIdMap[tenant.sub] = tenant.id;
      console.log(`Tenant: ${tenant.sub}, ID: ${tenant.id}`);
    });

    // Get all users
    const users = await User.findAll();
    console.log(`Found ${users.length} users`);

    // Update users with incorrect subId
    let updatedCount = 0;
    for (const user of users) {
      const correctTenantId = subToIdMap[user.sub];
      
      if (!correctTenantId) {
        console.log(`Warning: No tenant found for user ${user.email} with sub ${user.sub}`);
        continue;
      }

      // Check if subId is a string or doesn't match the correct tenant ID
      if (typeof user.subId === 'string' || user.subId !== correctTenantId) {
        console.log(`Fixing user ${user.email} (${user.id}): changing subId from ${user.subId} to ${correctTenantId}`);
        
        // Update the user directly in the database to avoid model validation issues
        await db.sequelize.query(`
          UPDATE "user" 
          SET "subid" = ${correctTenantId} 
          WHERE id = ${user.id}
        `);
        
        updatedCount++;
      }
    }

    console.log(`Updated ${updatedCount} users with correct tenant IDs`);
    console.log('Tenant ID fix completed successfully');
  } catch (error) {
    console.error('Error fixing tenant IDs:', error);
  } finally {
    // Close the database connection
    await db.sequelize.close();
  }
}

// Run the function
fixTenantIds();
