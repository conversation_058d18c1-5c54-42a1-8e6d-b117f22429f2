# Premier Kenya Subsidiary Fix

This document provides instructions on how to fix the login issues with the Premier Kenya subsidiary in the production environment.

## Issue Description

Users are unable to log in to the Premier Kenya subsidiary in the production environment, even though it works correctly in the local development environment. This is due to:

1. Missing or incorrect tenant and user records in the production database
2. A JavaScript error related to reassigning constant variables in the authentication controller

## Fix Instructions

### 1. Deploy Code Changes

First, make sure the following code changes are deployed to the production server:

- Change `const tenant` to `let tenant` in the following methods in `src/controllers/authController.js`:
  - `login` method (around line 274)
  - `verifyToken` method (around line 410)
  - `getCurrentUser` method (around line 522)

These changes fix the "Assignment to constant variable" error that occurs when trying to reassign the tenant variable.

### 2. Run the Production Fix Script

After deploying the code changes, run the production fix script to ensure the Premier Kenya tenant and users exist in the database with the correct configuration:

```bash
# Set the NODE_ENV to production
export NODE_ENV=production

# Run the fix script
node scripts/production-fix-premierkenya.js
```

This script will:
1. Check if the Premier Kenya tenant exists and create it if it doesn't
2. <PERSON>reate or update the admin user for Premier Kenya
3. Create or update other users (HR, IT, supervisor) for Premier Kenya
4. Ensure all users have the correct subsidiary and tenant ID

### 3. Verify the Fix

After running the script, try logging in to the Premier Kenya subsidiary with the following credentials:

- Email: <EMAIL>
- Password: Admin@123
- Subsidiary: premierkenya

If you still encounter issues, check the server logs for any errors and make sure the database changes were applied correctly.

## Additional Notes

- The script uses the database configuration from `src/db/config/config.js` for the production environment
- The script creates users with the following roles:
  - admin (systemAdmin)
  - hr
  - it
  - supervisor
- All users are created with the 'active' status
- The script is idempotent, meaning it can be run multiple times without causing issues

## Troubleshooting

If you encounter any issues:

1. Check the server logs for error messages
2. Verify that the database connection is working correctly
3. Make sure the tenant and user records exist in the database with the correct values
4. Ensure the code changes to fix the constant variable assignment error are deployed

If problems persist, you may need to manually check and update the database records using SQL queries.
