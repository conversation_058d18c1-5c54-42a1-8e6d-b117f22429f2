#!/bin/bash

# Script to run database seeders

echo "Running database seeders..."

# Run tenant seeder first
echo "Creating tenants..."
npx sequelize-cli db:seed --seed 20240501000000-tenant-seed.js

# Run user seeders for each subsidiary
echo "Creating users for Platinum Kenya..."
npx sequelize-cli db:seed --seed 20240501000001-platinumkenya-users.js

echo "Creating users for Premier Kenya..."
npx sequelize-cli db:seed --seed 20240501000002-premierkenya-users.js

echo "Creating users for Momentum Credit..."
npx sequelize-cli db:seed --seed 20240501000003-mometumcredit-users.js

echo "Creating users for Platinum Tanzania..."
npx sequelize-cli db:seed --seed 20240501000004-platinumtanzania-users.js

echo "Creating users for Premier Fanikiwa..."
npx sequelize-cli db:seed --seed 20240501000005-premierfanikiwa-users.js

echo "Creating users for Platinum Uganda..."
npx sequelize-cli db:seed --seed 20240501000006-platinumuganda-users.js

echo "Creating users for Premier Uganda..."
npx sequelize-cli db:seed --seed 20240501000007-premieruganda-users.js

echo "Creating users for Spectrum Zambia..."
npx sequelize-cli db:seed --seed 20240501000008-spectrumzambia-users.js

echo "Creating users for Premier South Africa..."
npx sequelize-cli db:seed --seed 20240501000009-premiersouthafrica-users.js

echo "Seeding completed successfully!"
