#!/bin/bash

# Production deployment script with database seeding

# Exit on error
set -e

echo "Starting production deployment..."

# Pull latest changes
echo "Pulling latest changes from repository..."
git pull origin main

# Install dependencies
echo "Installing dependencies..."
npm install

# Build frontend
echo "Building frontend..."
cd frontend && npm install && npm run build && cd ..

# Run migrations if needed
echo "Running database migrations..."
NODE_ENV=production npx sequelize-cli db:migrate

# Ask if seeding is needed
read -p "Do you want to run database seeds? (y/n): " run_seeds

if [[ $run_seeds == "y" || $run_seeds == "Y" ]]; then
  echo "Select seeding option:"
  echo "1. Run all seeds"
  echo "2. Run tenant seed only"
  echo "3. Run specific subsidiary user seeds"

  read -p "Enter option (1-3): " seed_option

  case $seed_option in
    1)
      echo "Running all seeds..."
      NODE_ENV=production npm run seed
      ;;
    2)
      echo "Running tenant seed only..."
      NODE_ENV=production npm run seed:tenant
      ;;
    3)
      echo "Select subsidiary to seed users for:"
      echo "1. Platinum Kenya"
      echo "2. Premier Group"
      echo "3. Momentum Credit"
      echo "4. Platinum Tanzania"
      echo "5. Premier Fanikiwa"
      echo "6. Platinum Uganda"
      echo "7. Spectrum Zambia"
      echo "8. Premier South Africa"

      read -p "Enter subsidiary (1-8): " subsidiary_option

      case $subsidiary_option in
        1) NODE_ENV=production npm run seed:users:platinumkenya ;;
        2) NODE_ENV=production npm run seed:users:premierkenya ;;
        3) NODE_ENV=production npm run seed:users:momentumcredit ;;
        4) NODE_ENV=production npm run seed:users:platinumtanzania ;;
        5) NODE_ENV=production npm run seed:users:premierfanikiwa ;;
        6) NODE_ENV=production npm run seed:users:platinumuganda ;;
        7) NODE_ENV=production npm run seed:users:spectrumzambia ;;
        8) NODE_ENV=production npm run seed:users:premiersouthafrica ;;
        *) echo "Invalid option" ;;
      esac
      ;;
    *)
      echo "Invalid option"
      ;;
  esac
fi

# Restart the application
echo "Restarting application..."
# If using PM2
pm2 restart digital-user-access-system

echo "Deployment completed successfully!"
