'use strict';
const bcrypt = require('bcryptjs');
const { Sequelize } = require('sequelize');
const config = require('../src/db/config/config');

// Get the configuration for the current environment
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Create a new Sequelize instance
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    logging: console.log,
  }
);

async function addPremierkenyaUser() {
  try {
    // Connect to the database
    await sequelize.authenticate();
    console.log('Connection to the database has been established successfully.');

    // Get the premierkenya tenant ID
    const [tenants] = await sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premierkenya'`
    );

    if (tenants.length === 0) {
      console.log('Premier Kenya tenant not found. Please run the tenant seeder first.');
      return;
    }

    const tenantId = tenants[0].id;
    console.log(`Found Premier Kenya tenant with ID: ${tenantId}`);

    // Hash password
    const salt = bcrypt.genSaltSync(10);
    const password = 'Admin@123';
    const hashedPassword = bcrypt.hashSync(password, salt);

    // Check if user already exists
    const [existingUsers] = await sequelize.query(
      `SELECT * FROM "user" WHERE email = '<EMAIL>'`
    );

    if (existingUsers.length > 0) {
      console.log('User already exists, skipping creation');
      return;
    }

    // Get the column names from the user table
    const [columns] = await sequelize.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'user' AND table_schema = 'public'
    `);

    console.log('User table columns:', columns.map(col => col.column_name));

    // Create a new user for premierkenya
    const [result] = await sequelize.query(`
      INSERT INTO "user" (
        username,
        email,
        password,
        sub,
        subid,
        employerid,
        role,
        status,
        createdat,
        updatedat
      )
      VALUES (
        'direct_admin',
        '<EMAIL>',
        '${hashedPassword}',
        'premierkenya',
        '${tenantId}',
        'EMP001',
        'systemAdmin',
        'active',
        NOW(),
        NOW()
      )
      RETURNING *;
    `);

    console.log('User created successfully:', result);
    console.log('\nLogin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin@123');
    console.log('Subsidiary: premierkenya');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await sequelize.close();
  }
}

// Run the function
addPremierkenyaUser();
