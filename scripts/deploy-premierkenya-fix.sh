#!/bin/bash

# <PERSON><PERSON>t to deploy the Premier Kenya subsidiary fix to the production server

# Display help message
show_help() {
  echo "Usage: $0 [options]"
  echo ""
  echo "Options:"
  echo "  -h, --help       Show this help message"
  echo "  -s, --server     Production server address (default: your-server.com)"
  echo "  -u, --user       SSH user (default: ubuntu)"
  echo "  -p, --path       Path to the application on the server (default: /var/www/digital-user-access)"
  echo ""
  echo "Example:"
  echo "  $0 -s uat-uap.platcorpgroup.com -u ubuntu -p /var/www/digital-user-access"
}

# Default values
SERVER="your-server.com"
USER="ubuntu"
APP_PATH="/var/www/digital-user-access"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -h|--help)
      show_help
      exit 0
      ;;
    -s|--server)
      SERVER="$2"
      shift 2
      ;;
    -u|--user)
      USER="$2"
      shift 2
      ;;
    -p|--path)
      APP_PATH="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      show_help
      exit 1
      ;;
  esac
done

echo "Deploying Premier Kenya subsidiary fix to $SERVER..."

# Create a temporary directory for the files to deploy
TEMP_DIR=$(mktemp -d)
mkdir -p "$TEMP_DIR/scripts"

# Copy the necessary files to the temporary directory
cp scripts/production-fix-premierkenya.js "$TEMP_DIR/scripts/"
cp scripts/README-PREMIERKENYA-FIX.md "$TEMP_DIR/scripts/"

# Create a patch file for the authController.js changes
cat > "$TEMP_DIR/auth-controller.patch" << 'EOF'
--- src/controllers/authController.js.orig
+++ src/controllers/authController.js
@@ -271,7 +271,7 @@
     }
 
     // Get tenant information
-    const tenant = await Tenant.findOne({
+    let tenant = await Tenant.findOne({
       where: { id: user.subId },
     });
 
@@ -407,7 +407,7 @@
     }
 
     // Get tenant information
-    const tenant = await Tenant.findOne({
+    let tenant = await Tenant.findOne({
       where: { id: user.subId },
     });
 
@@ -519,7 +519,7 @@
     const user = req.user;
 
     // Get tenant information
-    const tenant = await Tenant.findOne({
+    let tenant = await Tenant.findOne({
       where: { id: user.subId },
     });
 
EOF

# Create a deployment script to run on the server
cat > "$TEMP_DIR/deploy.sh" << 'EOF'
#!/bin/bash

# Script to apply the Premier Kenya subsidiary fix on the server

# Get the application path from the first argument
APP_PATH="$1"
if [ -z "$APP_PATH" ]; then
  echo "Error: Application path not provided"
  exit 1
fi

# Change to the application directory
cd "$APP_PATH" || { echo "Error: Could not change to application directory"; exit 1; }

# Apply the patch to the authController.js file
echo "Applying patch to authController.js..."
patch -p0 < auth-controller.patch

# Copy the fix script to the scripts directory
echo "Copying fix script..."
mkdir -p scripts
cp -f production-fix-premierkenya.js scripts/
cp -f README-PREMIERKENYA-FIX.md scripts/

# Set the NODE_ENV to production
export NODE_ENV=production

# Run the fix script
echo "Running the fix script..."
node scripts/production-fix-premierkenya.js

echo "Fix applied successfully!"
EOF

# Make the deployment script executable
chmod +x "$TEMP_DIR/deploy.sh"

# Copy the files to the server
echo "Copying files to the server..."
scp -r "$TEMP_DIR/"* "$USER@$SERVER:/tmp/"

# Run the deployment script on the server
echo "Running the deployment script on the server..."
ssh "$USER@$SERVER" "cd /tmp && ./deploy.sh $APP_PATH"

# Clean up the temporary directory
rm -rf "$TEMP_DIR"

echo "Deployment completed successfully!"
