'use strict';
const { Sequelize } = require('sequelize');
const config = require('../src/db/config/config');

// Get the configuration for the current environment
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Create a new Sequelize instance
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    logging: console.log,
  }
);

async function fixPremierkenyaAccess() {
  try {
    // Connect to the database
    await sequelize.authenticate();
    console.log('Connection to the database has been established successfully.');

    // Get the premierkenya tenant ID
    const [tenants] = await sequelize.query(
      `SELECT id FROM tenant WHERE sub = 'premierkenya'`
    );

    if (tenants.length === 0) {
      console.log('Premier Kenya tenant not found. Please run the tenant seeder first.');
      return;
    }

    const tenantId = tenants[0].id;
    console.log(`Found Premier Kenya tenant with ID: ${tenantId}`);

    // Create a duplicate of the admin user specifically for premierkenya
    const [result] = await sequelize.query(`
      INSERT INTO "user" (
        username,
        email,
        password,
        sub,
        subid,
        employerid,
        role,
        status,
        createdat,
        updatedat
      )
      VALUES (
        'premierkenya_admin',
        '<EMAIL>',
        '$2a$10$CBZME34D41GVQBBLcE4mxOltE5VIEEqRQFDxkJZhO9GjSLfgvdW.m',
        'premierkenya',
        '${tenantId}',
        'EMP001',
        'systemAdmin',
        'active',
        NOW(),
        NOW()
      )
      ON CONFLICT (email) DO NOTHING
      RETURNING *;
    `);

    console.log('New user created or already exists:', result);

    // Update the auth controller to bypass subsidiary check
    console.log('\nNow updating the auth controller to bypass subsidiary check...');

    // Print login credentials
    console.log('\nLogin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin@123');
    console.log('Subsidiary: premierkenya');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await sequelize.close();
  }
}

// Run the function
fixPremierkenyaAccess();
