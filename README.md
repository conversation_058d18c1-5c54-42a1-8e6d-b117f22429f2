# Digital User Access and Revocation System

## Overview

The Digital User Access and Revocation System is a web-based application designed to streamline and automate the process of granting and revoking user access within the organization. The solution eliminates the challenges of paper-based manual processes, ensuring efficiency, compliance, and security.

By leveraging digitized workflows and integrating with internal systems like Mambu, the project aims to offer improved traceability, data security, and a reliable audit trail for user access activities.

This repository contains the full-stack development of the system including the frontend, backend, and integration with third-party tools like DocuSign for digital signatures.

---

## Features

- **User Access Granting & Revocation**: Provides a simple way to request, approve, and revoke access for users within the system.
- **Role-Based Access Control**: Different user roles (requesters, approvers, administrators) with distinct permissions.
- **Audit Logs**: Tracks all actions, including creation, modification, approval, and revocation of access requests.
- **Digital Signatures**: Integrated DocuSign for the capture of digital signatures for user access requests and revocations.
- **Dashboard**: Displays system metrics like total users, pending requests, approved requests, and activity tracker.
- **Report Generation**: Generate various reports such as access history, audit logs, and pending approvals.
- **Mobile-Responsive Interface**: Optimized for desktop and mobile viewing.

---

## Installation

### Prerequisites

Before getting started, ensure you have the following installed:

- [Node.js](https://nodejs.org/) (v14 or higher)
- [npm](https://www.npmjs.com/) or [Yarn](https://yarnpkg.com/)
- [PostgreSQL](https://www.postgresql.org/) (for the database)

### Backend Setup

1. Clone the repository:

   ```bash
   git clone https://github.com/your-repository/digital-user-access-system.git
   cd digital-user-access-system
   ```
