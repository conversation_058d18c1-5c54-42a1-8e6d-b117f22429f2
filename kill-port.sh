#!/bin/bash

# Check if port number is provided
if [ -z "$1" ]; then
  echo "Usage: ./kill-port.sh <port_number>"
  exit 1
fi

PORT=$1
echo "Looking for processes using port $PORT..."

# Find the process ID using the port
PID=$(lsof -i :$PORT -t)

if [ -z "$PID" ]; then
  echo "No process found using port $PORT"
  exit 0
else
  echo "Found process(es) using port $PORT: $PID"
  echo "Killing process(es)..."
  
  # Kill the process
  kill -9 $PID
  
  echo "Process(es) killed successfully"
fi
