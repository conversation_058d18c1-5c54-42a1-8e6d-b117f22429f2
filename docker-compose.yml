name: Digital_User_Access
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: digital_user_access
    restart: always
    ports:
      - 7080:7080
    depends_on:
      dev-db:
        condition: service_healthy
        restart: true
    networks:
      - digitaluser_network

    volumes:
      - app:/var/lib/data
    env_file:
      - .env

  dev-db:
    image: postgres:14
    ports:
      - 5434:5030
    container_name: useracess_db
    environment:
      - POSTGRES_USER=digital_user_access
      - POSTGRES_PASSWORD=Ahadho13
      - POSTGRES_DB=digital_user_access
    volumes:
      - postgres:/var/lib/postgresql/data
    networks:
      - digitaluser_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U digital_user_access"]
      interval: 10s
      timeout: 5s
      retries: 5
  migrations:
      build:
        context: ./.
        dockerfile: Dockerfile
      restart: on-failure
      networks:
        - digitaluser_network
      depends_on:
        dev-db:
          condition: service_healthy
      env_file:
        - .env
      command:
        - sh
        - -c
        - npx sequelize-cli db:migrate
 
networks:
  digitaluser_network:
    driver: bridge
volumes:
  postgres:
  app:
