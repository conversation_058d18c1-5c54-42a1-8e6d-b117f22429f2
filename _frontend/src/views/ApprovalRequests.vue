<template>
  <div class="p-6 bg-white shadow-lg rounded-xl">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-2xl font-bold" :style="{ color: theme.primaryColor }">
        Pending Access Requests
      </h2>
      <input
        v-model="search"
        placeholder="Search requests..."
        class="border p-2 rounded w-1/3 shadow-sm focus:outline-none focus:ring"
        :style="{
          borderColor: theme.primaryColor,
          boxShadow: `0px 0px 4px ${theme.primaryColor}`,
        }"
      />
    </div>

    <div class="overflow-x-auto rounded-lg shadow-sm">
      <table class="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead :style="{ backgroundColor: theme.primaryColor, color: 'white' }">
          <tr>
            <th class="p-3 border">ID</th>
            <th class="p-3 border">Requestor</th>
            <th class="p-3 border">Department</th>
            <th class="p-3 border">Status</th>
            <th class="p-3 border text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="request in paginatedRequests"
            :key="request.id"
            class="hover:bg-gray-100 transition"
          >
            <td class="p-3 border">{{ request.id }}</td>
            <td class="p-3 border">
              {{ request.firstName }} {{ request.lastName }}
            </td>
            <td class="p-3 border">{{ request.department }}</td>
            <td
              class="p-3 border font-semibold"
              :class="statusClass(request.approvalStatus)"
            >
              {{ request.approvalStatus }}
            </td>
            <td class="p-3 border flex justify-center space-x-2">
              <button
                v-if="canApprove(request.approvalStatus)"
                @click="openApprovalModal(request)"
                class="bg-gradient-to-r from-green-400 to-green-600 text-white px-4 py-2 rounded shadow hover:scale-105 transition"
              >
                ✓ Approve
              </button>
              <button
                @click="rejectRequest(request.id)"
                class="bg-gradient-to-r from-red-400 to-red-600 text-white px-4 py-2 rounded shadow hover:scale-105 transition"
              >
                ✗ Reject
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination Controls -->
    <div class="flex justify-between items-center mt-4">
      <label class="text-sm">Items per page:</label>
      <select v-model="itemsPerPage" class="border p-2 rounded">
        <option
          v-for="option in paginationOptions"
          :key="option"
          :value="option"
        >
          {{ option }}
        </option>
      </select>
      <div>
        <button
          @click="prevPage"
          :disabled="currentPage === 1"
          class="px-4 py-2 mx-1 rounded bg-gray-300 hover:bg-gray-400 disabled:opacity-50"
          :style="{ backgroundColor: theme.primaryColor, color: 'white' }"
        >
          ◀ Previous
        </button>
        <span class="text-gray-700">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="px-4 py-2 mx-1 rounded bg-gray-300 hover:bg-gray-400 disabled:opacity-50"
          :style="{ backgroundColor: theme.primaryColor, color: 'white' }"
        >
          Next ▶
        </button>
      </div>
    </div>

    <!-- Approval Modal -->
    <div
      v-if="showApprovalModal"
      class="fixed inset-0 flex items-center justify-center bg-gray-200 bg-opacity-50 backdrop-blur-sm"
    >
      <div class="bg-white p-6 rounded-lg shadow-xl w-96">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">
          Approve Access Request
        </h3>
        <p class="text-gray-600 mb-4">
          Review the request details before approving.
        </p>

        <!-- Displaying Full Request Details -->
        <div class="bg-gray-100 p-3 rounded mb-4 text-sm">
          <p>
            <strong>Requestor:</strong> {{ selectedRequest?.firstName }}
            {{ selectedRequest?.lastName }}
          </p>
          <p><strong>System Name:</strong> {{ selectedRequest?.systemName }}</p>
          <p><strong>Branch:</strong> {{ selectedRequest?.branch }}</p>
          <p><strong>Email Address:</strong> {{ selectedRequest?.email }}</p>
          <p><strong>Telephone No:</strong> {{ selectedRequest?.telephone }}</p>
          <p><strong>Department:</strong> {{ selectedRequest?.department }}</p>
          <p><strong>Access Type:</strong> {{ selectedRequest?.accessType }}</p>
          <p>
            <strong>Role To Be Assigned:</strong> {{ selectedRequest?.role }}
          </p>
          <p>
            <strong>Previous Role:</strong> {{ selectedRequest?.previousRole }}
          </p>
          <p>
            <strong>Reason for Access:</strong> {{ selectedRequest?.reason }}
          </p>
        </div>

        <!-- Upload Signature -->
        <div class="mt-4">
          <p class="text-gray-600 mb-1 font-semibold">Upload Signature:</p>
          <input
            type="file"
            accept="image/*"
            @change="handleSignatureUpload"
            class="border p-2 rounded w-full bg-gray-50"
          />
        </div>

        <div class="flex justify-end space-x-2 mt-4">
          <button
            @click="approveRequest"
            class="bg-blue-500 text-white px-4 py-2 rounded shadow hover:bg-blue-600"
          >
            Submit Approval
          </button>
          <button
            @click="closeApprovalModal"
            class="bg-gray-500 text-white px-4 py-2 rounded shadow hover:bg-gray-600"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getAllAccessRequests,
  approveHR,
  approveExecutive,
  approveIT,
  rejectAccessRequest,
} from "@/services/apiService";
import { subsidiaries } from "@/config/subsidiaries";

export default {
  props: ["subsidiary"],
  computed: {
    theme() {
      return subsidiaries[this.subsidiary];
    },
    filteredRequests() {
      return this.requests.filter(
        (request) =>
          request.firstName.toLowerCase().includes(this.search.toLowerCase()) ||
          request.lastName.toLowerCase().includes(this.search.toLowerCase()) ||
          request.department.toLowerCase().includes(this.search.toLowerCase())
      );
    },
    totalPages() {
      return Math.ceil(this.filteredRequests.length / this.itemsPerPage);
    },
    paginatedRequests() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      return this.filteredRequests.slice(start, start + this.itemsPerPage);
    },
    paginationOptions() {
      return Array.from(
        { length: Math.ceil(this.requests.length / 10) },
        (_, i) => (i + 1) * 10
      );
    },
  },
  data() {
    return {
      requests: [],
      showApprovalModal: false,
      selectedRequest: null,
      search: "",
      itemsPerPage: 10,
      currentPage: 1,
      approver: { name: "", department: "", signature: "" },
      signatureFile: null,
    };
  },
  async created() {
    await this.fetchRequests();
  },
  methods: {
    async fetchRequests() {
      try {
        const data = await getAllAccessRequests(this.subsidiary);
        this.requests = data;
      } catch (error) {
        console.error("Error fetching requests:", error);
      }
    },
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    },
    statusClass(status) {
      return {
        "text-green-600": status.includes("Approved"),
        "text-red-600": status.includes("Rejected"),
        "text-yellow-500": status === "Pending",
      };
    },
    canApprove(status) {
      return ["Pending", "Approved by HR", "Approved by Executive"].includes(
        status
      );
    },
    openApprovalModal(request) {
      this.selectedRequest = request;
      this.showApprovalModal = true;
      this.signatureFile = null;
    },
    closeApprovalModal() {
      this.showApprovalModal = false;
    },
    handleSignatureUpload(event) {
      const file = event.target.files[0];
      if (file && file.type.startsWith("image/")) {
        this.signatureFile = file;
      } else {
        alert("Please upload a valid image file.");
      }
    },
    async convertToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
      });
    },
    async approveRequest() {
      if (!this.selectedRequest) return;
      try {
        if (!this.signatureFile) {
          alert("Please upload a signature before approving.");
          return;
        }

        const base64Signature = await this.convertToBase64(this.signatureFile);
        this.approver.signature = base64Signature;

        console.log("Approving request for ID:", this.selectedRequest.id);

        if (this.selectedRequest.approvalStatus === "Pending") {
          await approveHR(this.selectedRequest.id, this.subsidiary);
        } else if (this.selectedRequest.approvalStatus.includes("HR")) {
          await approveExecutive(this.selectedRequest.id, this.subsidiary);
        } else if (this.selectedRequest.approvalStatus.includes("Executive")) {
          await approveIT(this.selectedRequest.id, this.subsidiary);
        }

        this.closeApprovalModal();
        this.fetchRequests();
      } catch (error) {
        console.error("Error approving request:", error);
      }
    },
    async rejectRequest(requestId) {
      try {
        await rejectAccessRequest(requestId, this.subsidiary);
        this.fetchRequests();
      } catch (error) {
        console.error("Error rejecting request:", error);
      }
    },
  },
};
</script>
