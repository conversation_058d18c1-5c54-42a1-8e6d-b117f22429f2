<template>
  <div class="min-h-screen bg-gray-50 flex">
    <!-- Sidebar - hidden on mobile by default, shown on desktop if authenticated and not on auth page -->
    <MainSidebar
      v-if="!isAuthPage && isAuthenticated"
      :subsidiary="subsidiary"
      class="hidden md:block fixed inset-y-0 left-0 z-20 w-64 transition-transform duration-300 ease-in-out"
    />

    <!-- Main Content Area - takes full width on mobile, adjusted width on desktop -->
    <div
      class="flex-1 flex flex-col min-w-0 transition-all duration-300 ease-in-out"
      :class="{
        'md:pl-64': isAuthenticated && !isAuthPage
      }"
    >
      <!-- Navbar - shown if authenticated and not on auth page -->
      <MainNavbar
        v-if="!isAuthPage && isAuthenticated"
        :subsidiary="subsidiary"
        class="sticky top-0 z-10 w-full"
      />

      <!-- Page Content -->
      <main class="flex-1 relative overflow-y-auto focus:outline-none">
        <div class="py-6 px-4 sm:px-6 lg:px-8">
          <!-- Router View -->
          <router-view :subsidiary="subsidiary" />
        </div>
      </main>

      <!-- Footer - optional -->
      <footer v-if="isAuthenticated && !isAuthPage" class="bg-white border-t border-gray-200 p-4 text-center text-sm text-gray-500">
        <p>&copy; {{ new Date().getFullYear() }} Digital User Access System</p>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import MainNavbar from "@/components/MainNavbar.vue";
import MainSidebar from "@/components/MainSidebar.vue";

// Get route and store instances
const route = useRoute();
const store = useStore();

// Computed properties
const isAuthenticated = computed(() => store.getters['auth/isAuthenticated']);

const subsidiary = computed(() => {
  return route.params.subsidiary || "platinumkenya";
});

const isAuthPage = computed(() => {
  // Check if current route is an authentication page
  return (
    route.path === "/" ||
    route.path === "/user-access/ui/" ||
    route.path === "/login"
  );
});
</script>

<style>
/* Custom styles */

/* Button styles */
.btn-primary {
  padding: 0.5rem 1rem; /* px-4 py-2 */
  background-color: #2563eb; /* bg-blue-600 */
  color: white;
  border-radius: 0.375rem; /* rounded-md */
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #1d4ed8; /* bg-blue-700 */
}

/* Card styles */
.card {
  background-color: white; /* bg-white */
  border-radius: 0.5rem; /* rounded-lg */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-md */
  padding: 1.5rem; /* p-6 */
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f3f4f6; /* bg-gray-100 */
}

::-webkit-scrollbar-thumb {
  background-color: #9ca3af; /* bg-gray-400 */
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280; /* bg-gray-500 */
}
</style>
