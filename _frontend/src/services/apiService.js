import axios from "axios";
import config from "@/config";

let mambuUser = localStorage.getItem("mambuUser") ?? import.meta.env.VITE_APP_MAMBU_USER;

// Get token from localStorage
const token = localStorage.getItem('token');

// Axios instance
const api = axios.create({
  baseURL: config["BACKEND_SERVICE"],
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
    mambuUser,
    ...(token ? { 'Authorization': `Bearer ${token}` } : {})
  },
});

// Helper for dynamic headers
const getSubsidiaryHeader = (subsidiary) => ({
  headers: {
    "x-subsidiary": subsidiary,
  },
});

// Multipart header with subsidiary
const getMultipartHeaders = (subsidiary) => ({
  headers: {
    "Content-Type": "multipart/form-data",
    "x-subsidiary": subsidiary,
  },
});

// Response handler
const handleResponse = (response) => {
  console.log("API Response:", response);

  // Log more detailed information about the response
  if (response && response.data) {
    console.log("Response data structure:", {
      hasData: !!response.data,
      dataType: typeof response.data,
      isArray: Array.isArray(response.data),
      hasUsers: response.data.users ? true : false,
      hasUser: response.data.user ? true : false,
      keys: Object.keys(response.data),
      status: response.status,
      statusText: response.statusText
    });

    // For debugging - log the full response data
    console.log("Full response data:", JSON.stringify(response.data, null, 2));

    return response.data;
  } else {
    console.error("No data found in response");
    throw new Error("No data found in response");
  }
};

// Dashboard
export const getDashboardSummary = async (subsidiary) => {
  const response = await api.get(
    "/dashboard/dashboard-summary",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

export const getRecentActivities = async (subsidiary) => {
  const response = await api.get(
    "/dashboard/recent-activities",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

export const getActivityTracker = async (subsidiary) => {
  const response = await api.get(
    "/dashboard/activity-tracker",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

// Access Request Options
export const getBranches = async (subsidiary) => {
  const response = await api.get(
    "/access-request/branches",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data.branches || [];
};

export const getRoles = async (subsidiary) => {
  const response = await api.get(
    "/access-request/userroles",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data.roles || [];
};

export const getDepartments = async (subsidiary) => {
  const response = await api.get(
    "/access-request/departments",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data.dimensions || [];
};

// Reports
export const getReports = async (endpoint, subsidiary) => {
  const response = await api.get(endpoint, getSubsidiaryHeader(subsidiary));
  return handleResponse(response);
};

// Audit Logs
export const getAllAuditLogs = async (subsidiary) => {
  const response = await api.get(
    "/audit-logs",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

// Access
export const submitAccessForm = (formData, subsidiary) =>
  api.post("/access-request", formData, getMultipartHeaders(subsidiary));

export const submitRevocationForm = async (formData, subsidiary) => {
  const response = await api.post(
    "/revocation/submit",
    formData,
    getMultipartHeaders(subsidiary)
  );
  return response.data;
};

export const submitRevocationBulkUpload = async (formData, subsidiary) => {
  const response = await api.post(
    "/revocation/bulk-upload",
    formData,
    getMultipartHeaders(subsidiary)
  );
  return response.data;
};

// Access Requests
export const getAllAccessRequests = async (subsidiary) => {
  const response = await api.get(
    "/access-request",
    getSubsidiaryHeader(subsidiary)
  );
  return handleResponse(response);
};

export const getAccessRequestById = (id, subsidiary) =>
  api.get(`/access-request/${id}`, getSubsidiaryHeader(subsidiary));

export const updateAccessRequest = (id, data, subsidiary) =>
  api.put(`/access-request/${id}`, data, getSubsidiaryHeader(subsidiary));

export const deleteAccessRequest = (id, subsidiary) =>
  api.delete(`/access-request/${id}`, getSubsidiaryHeader(subsidiary));

export const approveHR = (id, subsidiary) =>
  api.put(
    `/access-request/${id}/approve-hr`,
    {},
    getSubsidiaryHeader(subsidiary)
  );

export const approveExecutive = (id, subsidiary) =>
  api.put(
    `/access-request/${id}/approve-executive`,
    {},
    getSubsidiaryHeader(subsidiary)
  );

export const approveIT = (id, subsidiary) =>
  api.put(
    `/access-request/${id}/approve-it`,
    {},
    getSubsidiaryHeader(subsidiary)
  );

export const rejectAccessRequest = (id, subsidiary) =>
  api.put(`/access-request/${id}/reject`, {}, getSubsidiaryHeader(subsidiary));

// Revocation
export const getPendingRevocations = async (subsidiary) => {
  const response = await api.get(
    "/revocation/pending",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data;
};

export const getAllRevocations = async (subsidiary) => {
  const response = await api.get(
    "/revocation/audit",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data;
};

export const getCompletedRevocations = async (subsidiary) => {
  const response = await api.get(
    "/revocation/completed",
    getSubsidiaryHeader(subsidiary)
  );
  return response.data;
};

export const approveRevocation = (id, subsidiary) =>
  api.put(`/revocation/${id}/approve`, {}, getSubsidiaryHeader(subsidiary));

export const rejectRevocation = (id, reason, subsidiary) =>
  api.put(
    `/revocation/${id}/reject`,
    { rejectionReason: reason },
    getSubsidiaryHeader(subsidiary)
  );

// User Management
export const getAllUsers = async (subsidiary) => {
  try {
    console.log('Getting all users for subsidiary:', subsidiary);
    const response = await api.get("/user", getSubsidiaryHeader(subsidiary));
    console.log('Raw getAllUsers response:', response);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in getAllUsers:', error);
    throw error;
  }
};

export const getUser = async (id, subsidiary) => {
  try {
    const response = await api.get(`/user/${id}`, getSubsidiaryHeader(subsidiary));
    return handleResponse(response);
  } catch (error) {
    console.error('Error in getUser:', error);
    throw error;
  }
};

export const createUser = async (userData, subsidiary) => {
  try {
    console.log('Creating user with data:', userData);
    const response = await api.post("/user", userData, getSubsidiaryHeader(subsidiary));
    console.log('Raw createUser response:', response);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in createUser:', error);
    throw error;
  }
};

export const updateUser = async (id, userData, subsidiary) => {
  try {
    const response = await api.put(`/user/${id}`, userData, getSubsidiaryHeader(subsidiary));
    return handleResponse(response);
  } catch (error) {
    console.error('Error in updateUser:', error);
    throw error;
  }
};

export const deleteUser = async (id, subsidiary) => {
  try {
    const response = await api.delete(`/user/${id}`, getSubsidiaryHeader(subsidiary));
    return handleResponse(response);
  } catch (error) {
    console.error('Error in deleteUser:', error);
    throw error;
  }
};

// Authentication
export const login = (credentials, subsidiary) => {
  // Ensure subsidiary is included in both the request body and headers
  return api.post("/auth/login", credentials, getSubsidiaryHeader(subsidiary));
};

export const verifyToken = (token, subsidiary) =>
  api.post("/auth/verify-token", { token }, getSubsidiaryHeader(subsidiary));

export const getCurrentUser = (subsidiary) =>
  api.get("/auth/me", getSubsidiaryHeader(subsidiary));

// Tenant Management
export const getAllTenants = async () => {
  try {
    console.log('Fetching all tenants');
    const response = await api.get("/tenant");
    console.log('Raw getAllTenants response:', response);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in getAllTenants:', error);
    throw error;
  }
};

export const getTenant = async (id) => {
  try {
    const response = await api.get(`/tenant/${id}`);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in getTenant:', error);
    throw error;
  }
};

export const createTenant = async (tenantData) => {
  try {
    console.log('Creating tenant with data:', tenantData);
    const response = await api.post("/tenant", tenantData);
    console.log('Raw createTenant response:', response);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in createTenant:', error);
    throw error;
  }
};

export const updateTenant = async (id, tenantData) => {
  try {
    const response = await api.put(`/tenant/${id}`, tenantData);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in updateTenant:', error);
    throw error;
  }
};

export const deleteTenant = async (id) => {
  try {
    const response = await api.delete(`/tenant/${id}`);
    return handleResponse(response);
  } catch (error) {
    console.error('Error in deleteTenant:', error);
    throw error;
  }
};

export default api;
