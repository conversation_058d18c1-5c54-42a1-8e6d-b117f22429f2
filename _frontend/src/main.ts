import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import axios from "axios";
import config from "./config";
import "./style.css";
// import vuetify from "./plugins/vuetify";

let mambuUser = localStorage.getItem("mambuUser") ?? import.meta.env.VITE_APP_MAMBU_USER;
console.log("mm", import.meta.env.VITE_APP_MAMBU_USER )

// If using environment variables, you can set axios base URL here
axios.defaults.baseURL = config["BACKEND_SERVICE"];
axios.defaults.headers["mambuUser"] = mambuUser;

// Add a request interceptor
axios.interceptors.request.use(
  function (config) {
    // Do something before request is sent
    const isSearching = store.state.isSearching;

    if (isSearching) return config;
    store.dispatch("isLoading", true);
    return config;
  },
  function (error) {
    store.dispatch("isLoading", false).then(() => {
      store.dispatch(
        "toggleModal",
        error.response.data.message || error.message || error
      );
    });
    // Do something with request error
    return Promise.reject(error);
  }
);

// Add a response interceptor
axios.interceptors.response.use(
  function (response) {
    // Handle successful responses
    store.dispatch("isLoading", false);
    return response;
  },
  function (error) {
    // Handle error responses
    console.log(JSON.stringify(error, null, 3));
    // Redirect to 401.vue if unauthorized
    if (
      error.response &&
      (error.response.status === 401 || error.response.status === 403)
    ) {
      router.replace({
        name: "401",
      });
    }

    store.dispatch("isLoading", false).then(() => {
      store.dispatch("toggleModal", {
        message: error.response.data.message || error.message || error,
      });
    });

    return Promise.reject(error);
  }
);

// Initialize auth state from localStorage if token exists
const token = localStorage.getItem('token');
if (token) {
  // Set authorization header for all future requests
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

  // Verify the token and load user data
  store.dispatch('auth/verifyToken')
    .then(result => {
      console.log('Token verification result:', result);
    })
    .catch(error => {
      console.error('Token verification failed:', error);
      // If token verification fails, clear auth state
      store.dispatch('auth/logout');
    });
}

const app = createApp(App);
// app.use(vuetify);
app.use(router);
app.use(store);

app.mount("#app");
