import { createRouter, createWebHistory } from "vue-router";
import UserLogin from "@/views/UserLogin.vue";
import Dashboard from "@/views/UserDashboard.vue";
import RequestForm from "@/views/RequestForm.vue";
import Approval from "@/views/ApprovalRequests.vue";
import AuditLog from "@/views/AuditLog.vue";
import Reports from "@/views/UserReports.vue";
import VerifyLogin from "@/views/VerifyLogin.vue";
import UserManagement from "@/views/UserManagement.vue";
import TenantManagement from "@/views/TenantManagement.vue";
import store from "@/store";
import { canAccess } from "@/utils/accessControl";

// Import Revocation Components
import RevokeForm from "@/views/RevokeForm.vue";
import AccessRemoval from "@/views/AccessRemoval.vue";
import RevocationAudit from "@/views/RevocationAudit.vue";
import RevokedUsersReport from "@/views/RevokedUsersReport.vue";

const routes = [
  // Auth routes
  {
    path: "/login",
    name: "Login",
    component: UserLogin,
    meta: { requiresAuth: false }
  },
  {
    path: "/",
    redirect: "/login",
    // component: VerifyLogin,
    // props: (route) => ({
    //   email: route.query.email,
    //   eid: route.query.eid,
    //   sub: route.query.sub,
    // }),
    // meta: { requiresAuth: false }
  },

  // Protected routes
  {
    path: "/dashboard/:subsidiary",
    component: Dashboard,
    props: true,
    meta: { requiresAuth: true }
  },
  {
    path: "/request/:subsidiary",
    component: RequestForm,
    props: true,
    meta: { requiresAuth: true, resource: 'request-form' }
  },
  {
    path: "/approval/:subsidiary",
    component: Approval,
    props: true,
    meta: { requiresAuth: true, resource: 'approval' }
  },
  {
    path: "/audit/:subsidiary",
    component: AuditLog,
    props: true,
    meta: { requiresAuth: true, resource: 'audit' }
  },
  {
    path: "/users/:subsidiary",
    component: UserManagement,
    props: true,
    meta: { requiresAuth: true, resource: 'users' }
  },
  {
    path: "/tenants/:subsidiary",
    component: TenantManagement,
    props: true,
    meta: { requiresAuth: true, resource: 'tenants' }
  },
  {
    path: "/reports/:subsidiary",
    component: Reports,
    props: true,
    meta: { requiresAuth: true, resource: 'reports' }
  },

  // Revocation Section Routes
  {
    path: "/revoke-form/:subsidiary",
    component: RevokeForm,
    props: true,
    meta: { requiresAuth: true, resource: 'revoke-form' }
  },
  {
    path: "/access-removal/:subsidiary",
    component: AccessRemoval,
    props: true,
    meta: { requiresAuth: true, resource: 'access-removal' }
  },
  {
    path: "/revocation-audit/:subsidiary",
    component: RevocationAudit,
    props: true,
    meta: { requiresAuth: true, resource: 'revocation-audit' }
  },
  {
    path: "/revoked-users/:subsidiary",
    component: RevokedUsersReport,
    props: true,
    meta: { requiresAuth: true, resource: 'revoked-users-report' }
  },
  // wild card route
  // {
  //   path: "/:pathMatch(.*)*",
  //   component: VerifyLogin,
  //   props: (route) => ({
  //     email: route.query.email,
  //     eid: route.query.eid,
  //     sub: route.query.sub,
  //   }),
  //   meta: { requiresAuth: false }
  // },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Navigation guard to check authentication and role-based access
router.beforeEach((to, _, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const isAuthenticated = store.getters['auth/isAuthenticated'];
  const currentUser = store.getters['auth/currentUser'];

  // If route requires auth and user is not authenticated
  if (requiresAuth && !isAuthenticated) {
    next('/login');
    return;
  }

  // Check for role-based access using the resource metadata
  const resource = to.meta.resource;
  if (requiresAuth && resource && currentUser) {
    // If the user doesn't have access to this resource
    if (!canAccess(resource, currentUser)) {
      console.warn(`User ${currentUser.username} with role ${currentUser.role} denied access to ${resource}`);
      // Redirect to dashboard with the current subsidiary
      const subsidiary = to.params.subsidiary || 'platinumkenya';
      next(`/dashboard/${subsidiary}`);
      return;
    }
  }

  // Allow navigation
  next();
});

export default router;
